import React, { useState, useEffect } from 'react';
import { ShoppingBag, Leaf, Tag } from "lucide-react";
import { mercaditoApi, mercaditoSettingsApi, getFormattedDate } from '@/lib/mercadito-api';

const MercaditoSection = () => {
  const [availableProducts, setAvailableProducts] = useState<string[]>([]);
  const [isEnabled, setIsEnabled] = useState(true);
  const [currentDate, setCurrentDate] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadMercaditoData = async () => {
      try {
        setIsLoading(true);

        // Load settings and available products
        const [settings, products] = await Promise.all([
          mercaditoSettingsApi.get(),
          mercaditoApi.getAvailable()
        ]);

        setIsEnabled(settings.enabled);
        setCurrentDate(getFormattedDate(settings.custom_date));
        setAvailableProducts(products.map(p => p.nombre));
      } catch (error) {
        console.error('Error loading mercadito data:', error);
        // Fallback to disabled state
        setIsEnabled(false);
        setAvailableProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadMercaditoData();
  }, []);

  // Don't render the section if still loading
  if (isLoading) {
    return null;
  }

  // Show closed image if mercadito is disabled
  if (!isEnabled) {
    return (
      <section id="mercadito" className="pt-8 pb-16 bg-white">
        <div className="container mx-auto px-4">
          {/* Título con logo del mercadito - mismo estilo que cuando está abierto */}
          <div className="text-center mb-4 animate-fade-in">
            <div className="max-w-md mx-auto mb-2">
              <img
                src="/images/mercaditoip.png"
                alt="El Mercadito"
                className="w-full h-auto animate-float"
              />
            </div>
          </div>

          {/* Imagen de cerrado con el mismo estilo que la sección de productos */}
          <div className="max-w-4xl mx-auto mb-4">
            <div className="bg-gray-600 p-6 rounded-xl shadow-lg transform hover:scale-102 transition-all">
              <div className="flex items-center justify-center mb-3">
                <div className="w-4 h-4 bg-white rounded-full mr-2 animate-pulse"></div>
                <div>
                  <h4 className="text-xl font-pearly text-white">Mercadito Cerrado</h4>
                </div>
              </div>

              <div className="bg-white/10 p-4 rounded-lg text-center">
                {/* Imagen de cerrado con animación de entrada */}
                <img
                  src="/images/cerrado.png"
                  alt="Mercadito Cerrado"
                  className="w-full h-auto max-w-xs mx-auto mb-4 hover:scale-105 transition-transform duration-300"
                  style={{
                    animation: 'fadeInUp 0.8s ease-out'
                  }}
                />
              </div>

              <p className="text-white/80 mt-3 text-sm italic text-center" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
                El mercadito no está disponible en este momento. ¡Vuelve pronto para ver nuestros productos frescos!
              </p>
            </div>
          </div>
        </div>

        {/* Estilos para la animación de entrada */}
        <style jsx>{`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}</style>
      </section>
    );
  }

  // Don't render if no products available
  if (availableProducts.length === 0) {
    return null;
  }

  return (
    <section id="mercadito" className="pt-8 pb-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-4 animate-fade-in">
          <div className="max-w-md mx-auto mb-2">
            <img
              src="/images/mercaditoip.png"
              alt="El Mercadito"
              className="w-full h-auto animate-float"
            />
          </div>
        </div>

        <div className="max-w-4xl mx-auto mb-4">
          <div className="bg-cantina-600 p-6 rounded-xl shadow-lg transform hover:scale-102 transition-all">
            <div className="flex items-center mb-3">
              <div className="w-4 h-4 bg-white rounded-full mr-2 animate-pulse"></div>
              <div>
                <h4 className="text-xl font-pearly text-white">Disponible Hoy</h4>
                <p className="text-white/80 text-sm font-normal" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
                  {currentDate}
                </p>
              </div>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <ul className="space-y-2 text-white" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
                {availableProducts.map((product, index) => (
                  <li key={index} className="flex items-center">
                    <div className="w-2 h-2 bg-white rounded-full mr-2"></div>
                    <span>{product}</span>
                  </li>
                ))}
              </ul>
            </div>
            <p className="text-white/80 mt-3 text-sm italic" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
              ¡Productos frescos elaborados hoy! Cantidades limitadas.
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-1 gap-6 items-center max-w-4xl mx-auto">

          <div className="space-y-6 animate-fade-in animation-delay-200">
            <h3 className="text-3xl text-cantina-800 font-pearly">Productos Seleccionados</h3>
            <p className="text-lg text-gray-800" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
              En nuestro mercadito encontrarás una selección de productos frescos y artesanales
              cuidadosamente elegidos para que puedas disfrutar de la calidad de Cien Fuegos en tu hogar.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              {[
                {
                  icon: <ShoppingBag className="mx-auto text-cantina-600" size={32} />,
                  title: "Productos Frescos"
                },
                {
                  icon: <Leaf className="mx-auto text-cantina-600" size={32} />,
                  title: "Artesanales"
                },
                {
                  icon: <Tag className="mx-auto text-cantina-600" size={32} />,
                  title: "Precios Justos"
                }
              ].map((item, index) => (
                <div key={index} className="text-center p-4 transform hover:scale-105 transition-all duration-300 animate-float" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className="bg-cantina-50 rounded-full p-4 inline-block mb-3">
                    {item.icon}
                  </div>
                  <h4 className="text-lg mt-3 mb-2 font-pearly text-cantina-800">{item.title}</h4>
                </div>
              ))}
            </div>



            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div className="rounded-xl overflow-hidden shadow-xl transform hover:scale-105 transition-all h-64">
                <img
                  src="/images/baba-ganoush.webp"
                  alt="Baba Ganoush"
                  className="w-full h-full object-cover"
                  loading="eager"
                />
              </div>
              <div className="rounded-xl overflow-hidden shadow-xl transform hover:scale-105 transition-all h-64">
                <img
                  src="/images/hummus-aceitunas.webp"
                  alt="Hummus con aceitunas"
                  className="w-full h-full object-cover"
                  loading="eager"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MercaditoSection;
