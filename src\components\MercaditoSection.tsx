import { ShoppingBag, Leaf, Tag } from "lucide-react";

const MercaditoSection = () => {
  return (
    <section id="mercadito" className="pt-8 pb-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-4 animate-fade-in">
          <div className="max-w-md mx-auto mb-2">
            <img
              src="/images/mercaditoip.png"
              alt="El Mercadito"
              className="w-full h-auto animate-float"
            />
          </div>
        </div>

        <div className="max-w-4xl mx-auto mb-4">
          <div className="bg-cantina-600 p-6 rounded-xl shadow-lg transform hover:scale-102 transition-all">
            <div className="flex items-center mb-3">
              <div className="w-4 h-4 bg-white rounded-full mr-2 animate-pulse"></div>
              <h4 className="text-xl font-pearly text-white">Disponible Hoy</h4>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <ul className="space-y-2 text-white" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-white rounded-full mr-2"></div>
                  <span>Baba Ganoush casero</span>
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-white rounded-full mr-2"></div>
                  <span>Focaccia con romero y aceite de oliva</span>
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-white rounded-full mr-2"></div>
                  <span>Pancitos artesanales</span>
                </li>
              </ul>
            </div>
            <p className="text-white/80 mt-3 text-sm italic" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
              ¡Productos frescos elaborados hoy! Cantidades limitadas.
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-1 gap-6 items-center max-w-4xl mx-auto">

          <div className="space-y-6 animate-fade-in animation-delay-200">
            <h3 className="text-3xl text-cantina-800 font-pearly">Productos Seleccionados</h3>
            <p className="text-lg text-gray-800" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
              En nuestro mercadito encontrarás una selección de productos frescos y artesanales
              cuidadosamente elegidos para que puedas disfrutar de la calidad de Cien Fuegos en tu hogar.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              {[
                {
                  icon: <ShoppingBag className="mx-auto text-cantina-600" size={32} />,
                  title: "Productos Frescos",
                  description: "Frutas y verduras de temporada seleccionadas diariamente"
                },
                {
                  icon: <Leaf className="mx-auto text-cantina-600" size={32} />,
                  title: "Artesanales",
                  description: "Productos elaborados con técnicas tradicionales"
                },
                {
                  icon: <Tag className="mx-auto text-cantina-600" size={32} />,
                  title: "Precios Justos",
                  description: "Calidad a precios accesibles para todos"
                }
              ].map((item, index) => (
                <div key={index} className="text-center p-4 transform hover:scale-105 transition-all duration-300 animate-float" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className="bg-cantina-50 rounded-full p-4 inline-block mb-3">
                    {item.icon}
                  </div>
                  <h4 className="text-lg mt-3 mb-2 font-pearly text-cantina-800">{item.title}</h4>
                  <p className="text-gray-800" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>{item.description}</p>
                </div>
              ))}
            </div>



            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div className="rounded-xl overflow-hidden shadow-xl transform hover:scale-105 transition-all h-64">
                <img
                  src="/images/baba-ganoush.webp"
                  alt="Baba Ganoush"
                  className="w-full h-full object-cover"
                  loading="eager"
                />
              </div>
              <div className="rounded-xl overflow-hidden shadow-xl transform hover:scale-105 transition-all h-64">
                <img
                  src="/images/hummus-aceitunas.webp"
                  alt="Hummus con aceitunas"
                  className="w-full h-full object-cover"
                  loading="eager"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MercaditoSection;
