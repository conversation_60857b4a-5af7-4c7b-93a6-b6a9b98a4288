import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Lock } from 'lucide-react';

// Contraseña simple para acceder a la administración
// En un entorno de producción, deberías usar un sistema de autenticación más seguro
const ADMIN_PASSWORD = 'admin123';

interface AdminAuthProps {
  onAuthenticated: () => void;
}

const AdminAuth: React.FC<AdminAuthProps> = ({ onAuthenticated }) => {
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    try {
      if (password === ADMIN_PASSWORD) {
        // Initialize Supabase client
        const { supabase } = await import('@/lib/supabase');
        
        // Simple check if Supabase is configured
        const { data, error } = await supabase.from('platos_menu').select('count').single();
        if (error) {
          console.error('Error de conexión con Supabase:', error);
          setError('Error de conexión');
          toast({
            title: 'Error de conexión',
            description: 'No se pudo conectar con la base de datos. Por favor intenta más tarde.',
            variant: 'destructive',
          });
          return;
        }
        
        toast({
          title: 'Acceso concedido',
          description: 'Has iniciado sesión correctamente',
        });
        onAuthenticated();
      } else {
        setError('Contraseña incorrecta');
        toast({
          title: 'Error de autenticación',
          description: 'La contraseña ingresada es incorrecta',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error en autenticación:', error);
      if (!error.message?.includes('configuración')) {
        setError('Error al iniciar sesión');
        toast({
          title: 'Error de autenticación',
          description: 'Hubo un problema al iniciar sesión. Por favor intenta nuevamente.',
          variant: 'destructive',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <div className="bg-primary/10 p-3 rounded-full">
              <Lock className="h-6 w-6 text-primary" />
            </div>
          </div>
          <CardTitle className="text-2xl text-center">Administración</CardTitle>
          <CardDescription className="text-center">
            Ingresa la contraseña para acceder al panel de administración
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">Contraseña</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Ingresa la contraseña"
                required
              />
              {error && <p className="text-sm text-red-500">{error}</p>}
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Verificando...' : 'Acceder'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default AdminAuth;
