import { createClient } from '@supabase/supabase-js';

// Configuration for Supabase
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "";
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_KEY || "";
const SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZqc2pqdGt4bWZobWd3eGR6YmZhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTcyMTkyMiwiZXhwIjoyMDYxMjk3OTIyfQ.YIBQucjQpnyXEnhqsemmwGI_-yf9ivbmIVwSTM196j0";

// Create client for public operations (reading)
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true
  }
});

// Create admin client for write operations (bypasses RLS)
export const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Initialize admin session
export const initializeSupabase = async () => {
  try {
    // For admin operations, we'll use the service role client
    // which bypasses RLS policies
    console.log('Supabase admin client initialized');
    return true;
  } catch (error) {
    console.error('Supabase initialization error:', error);
    throw error;
  }
};
