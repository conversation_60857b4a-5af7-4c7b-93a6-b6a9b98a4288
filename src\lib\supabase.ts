import { createClient } from '@supabase/supabase-js';

// Configuration for Supabase
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "";
const SUPABASE_KEY = import.meta.env.VITE_SUPABASE_KEY || "";

// Create a single instance of the Supabase client
export const supabase = createClient(SUPABASE_URL, SUPABASE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true
  }
});

// Initialize admin session
export const initializeSupabase = async () => {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (!session || sessionError) {
      // Admin authentication using credentials
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email: import.meta.env.VITE_SUPABASE_ADMIN_EMAIL || '',
        password: import.meta.env.VITE_SUPABASE_ADMIN_PASSWORD || '',
      });
      
      if (signInError || !data.user) {
        console.error('Admin auth failed:', signInError?.message);
        throw new Error('Authentication failed');
      }
    }

    // Set up auth state change listener
    supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT' || (event === 'TOKEN_REFRESHED' && !session)) {
        // Re-initialize auth if session is lost
        initializeSupabase();
      }
    });
  } catch (error) {
    console.error('Supabase initialization error:', error);
    throw error;
  }
};
