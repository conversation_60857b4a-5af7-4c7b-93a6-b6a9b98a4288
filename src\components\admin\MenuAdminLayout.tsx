import React, { useEffect, useState } from 'react';
import { DragDropProvider, useDragDrop } from './DragDropContext';
import { DishForm } from './DishForm';
import { PlatoMenu } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { ArrowLeft, LogOut, RefreshCw, Plus, Edit2 } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Reorder } from 'framer-motion';

// Estilos utilitarios para mobile y consistencia visual
const panelBg = 'bg-white dark:bg-neutral-900 shadow-md rounded-lg';
const diaColBase = 'flex-1 min-w-[220px] sm:min-w-[180px] mx-1 sm:mx-2';
const scrollX = 'overflow-x-auto flex-nowrap';
const scrollY = 'overflow-y-auto max-h-[60vh]';

export const MenuAdminLayout: React.FC = () => {
  return (
    <DragDropProvider>
      <MenuAdminContent />
    </DragDropProvider>
  );
};

const MenuAdminContent: React.FC = () => {
  const { platos, setPlatos, platosPorDia, reordenarPlatos, cargarDatos, diasSemana } = useDragDrop();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingDish, setEditingDish] = useState<PlatoMenu | null>(null);
  const [selectedDia, setSelectedDia] = useState<number | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    cargarDatos(false);
  }, [cargarDatos]);

  const handleLogout = () => {
    localStorage.removeItem('admin_authenticated');
    navigate('/');
  };

  const handleRefresh = () => {
    cargarDatos(true);
  };

  const handleEditDish = (plato: PlatoMenu) => {
    setEditingDish(plato);
    setIsDialogOpen(true);
  };

  const handleNewDish = (dia?: number | null) => {
    setEditingDish(null);
    setSelectedDia(dia ?? null);
    setIsDialogOpen(true);
  };

  // Renderiza los platos de un día específico
  const renderDiaColumn = (dia: { id: number; nombre: string }) => {
    const platosDia = platosPorDia(dia.id === 0 ? null : dia.id);
    return (
      <Card key={dia.id} className={`${panelBg} ${diaColBase} flex flex-col mb-4`}>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-base sm:text-lg font-semibold">{dia.nombre}</CardTitle>
          <Button size="icon" variant="ghost" onClick={() => handleNewDish(dia.id === 0 ? null : dia.id)} aria-label="Agregar plato">
            <Plus className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className={`${scrollY} flex-1 p-2 sm:p-4`}> 
          <Reorder.Group
            axis="y"
            values={platosDia}
            onReorder={async (newOrder) => {
              await reordenarPlatos(newOrder, dia.id === 0 ? null : dia.id);
            }}
            className="space-y-2"
          >
            {platosDia.map((plato) => (
              <div key={plato.id} className="flex items-center justify-between bg-neutral-50 dark:bg-neutral-800 rounded shadow p-2 cursor-move border border-neutral-200 dark:border-neutral-700">
                <div className="flex flex-col gap-1">
                  <span className="font-medium text-sm sm:text-base">{plato.nombre}</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {plato.categorias?.map((cat) => (
                      <span key={cat} className="bg-primary/10 text-primary text-xs rounded px-2 py-0.5">{cat}</span>
                    ))}
                  </div>
                </div>
                <Button size="icon" variant="ghost" onClick={() => handleEditDish(plato)} aria-label="Editar plato">
                  <Edit2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </Reorder.Group>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto py-4 px-1 sm:px-4">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2 sm:gap-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-primary">Panel de Menú Semanal</h1>
          <p className="text-gray-500 text-sm sm:text-base">Arrastra los platos al día correspondiente para armar el menú.</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to="/">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver al sitio
            </Link>
          </Button>
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Recargar
          </Button>
          <Button variant="destructive" onClick={handleLogout}>
            <LogOut className="h-4 w-4 mr-2" />
            Salir
          </Button>
        </div>
      </div>
      <Separator className="my-4" />
      {/* Sección para crear platos */}
      <section className={`${panelBg} p-4 mb-4 w-full max-w-2xl mx-auto flex flex-col gap-2`}>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold text-primary">Crear nuevo plato</h2>
          <Button size="sm" onClick={() => handleNewDish(null)}>
            <Plus className="h-4 w-4 mr-1" /> Nuevo Plato
          </Button>
        </div>
        <p className="text-xs text-gray-500 mb-2">Agrega un plato y asígnale una o más categorías (etiquetas).</p>
      </section>
      {/* Días de la semana en scroll horizontal para mobile */}
      <div className={`flex flex-row gap-2 sm:gap-4 ${scrollX} pb-2`}>{diasSemana.map(renderDiaColumn)}</div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-lg w-full max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{editingDish ? 'Editar Plato' : 'Nuevo Plato'}</DialogTitle>
          </DialogHeader>
          <DishForm plato={editingDish} onSuccess={() => setIsDialogOpen(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
};
