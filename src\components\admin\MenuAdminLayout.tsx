import React, { useEffect, useState } from 'react';
import { DragDropProvider, useDragDrop } from './DragDropContext';
import { DishForm } from './DishForm';
import { DishList } from './DishList';
import { MobileBottomNav } from './MobileBottomNav';
import { PlatoMenu } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, LogOut, RefreshCw, Plus, Edit2, Calendar, List, Menu, X, GripVertical } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

export const MenuAdminLayout: React.FC = () => {
  return (
    <DragDropProvider>
      <MenuAdminContent />
    </DragDropProvider>
  );
};

// Componente para elementos arrastrables
interface SortableDishItemProps {
  plato: PlatoMenu;
  onEdit: (plato: PlatoMenu) => void;
}

const SortableDishItem: React.FC<SortableDishItemProps> = ({ plato, onEdit }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: plato.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="group bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200"
    >
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-start gap-2 flex-1 min-w-0">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing mt-1 touch-target"
          >
            <GripVertical className="h-4 w-4 text-gray-400" />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm text-gray-900 dark:text-white truncate">
              {plato.nombre}
            </h4>
            {plato.descripcion && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                {plato.descripcion}
              </p>
            )}
            <div className="flex flex-wrap gap-1 mt-2">
              <Badge variant="outline" className="text-xs">
                {plato.tipo}
              </Badge>
              {plato.destacado && (
                <Badge className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  ⭐ Destacado
                </Badge>
              )}
              {plato.sin_gluten && (
                <Badge variant="secondary" className="text-xs">
                  Sin gluten
                </Badge>
              )}
              {plato.vegetariano && (
                <Badge variant="secondary" className="text-xs">
                  🌱 Vegetariano
                </Badge>
              )}
            </div>
          </div>
        </div>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => onEdit(plato)}
          className="h-8 w-8 p-0 opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity touch-target"
        >
          <Edit2 className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
};

// Componente para columnas que pueden recibir elementos
interface DroppableColumnProps {
  id: string;
  children: React.ReactNode;
  className?: string;
}

const DroppableColumn: React.FC<DroppableColumnProps> = ({ id, children, className }) => {
  const { setNodeRef, isOver } = useDroppable({
    id,
  });

  return (
    <div
      ref={setNodeRef}
      className={`${className} ${isOver ? 'ring-2 ring-blue-400 ring-opacity-50' : ''}`}
    >
      {children}
    </div>
  );
};

const MenuAdminContent: React.FC = () => {
  const { platos, setPlatos, platosPorDia, reordenarPlatos, moverPlatoEntreDias, cargarDatos, diasSemana } = useDragDrop();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingDish, setEditingDish] = useState<PlatoMenu | null>(null);
  const [selectedDia, setSelectedDia] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState('menu');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDragId, setActiveDragId] = useState<number | null>(null);
  const navigate = useNavigate();

  // Configurar sensores para el drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    cargarDatos(false);
  }, [cargarDatos]);

  const handleLogout = () => {
    localStorage.removeItem('admin_authenticated');
    navigate('/');
  };

  const handleRefresh = () => {
    cargarDatos(true);
  };

  const handleEditDish = (plato: PlatoMenu) => {
    setEditingDish(plato);
    setIsDialogOpen(true);
  };

  const handleNewDish = (dia?: number | null = null) => {
    setEditingDish(null);
    setSelectedDia(dia ?? null);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingDish(null);
    setSelectedDia(null);
  };

  // Funciones para el drag and drop
  const handleDragStart = (event: DragStartEvent) => {
    setActiveDragId(event.active.id as number);
  };

  const handleDragOver = (event: DragOverEvent) => {
    // Aquí podríamos agregar lógica adicional si necesitamos feedback visual durante el arrastre
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveDragId(null);

    if (!over) return;

    const activeId = active.id as number;
    const overId = over.id;

    // Encontrar el plato que se está arrastrando
    const activePlato = platos.find(p => p.id === activeId);
    if (!activePlato) return;

    // Determinar si se está moviendo dentro del mismo día o entre días diferentes
    if (typeof overId === 'string' && overId.startsWith('day-')) {
      // Se está soltando en una columna de día
      const targetDayId = parseInt(overId.replace('day-', ''));
      const targetDay = targetDayId === 0 ? null : targetDayId;
      const sourceDay = activePlato.dia_semana;

      if (sourceDay !== targetDay) {
        // Mover entre días diferentes
        const targetDayPlatos = platosPorDia(targetDay);
        moverPlatoEntreDias(activeId, sourceDay, targetDay, targetDayPlatos.length);
      }
    } else if (typeof overId === 'number') {
      // Se está soltando sobre otro plato
      const overPlato = platos.find(p => p.id === overId);
      if (!overPlato) return;

      const sourceDay = activePlato.dia_semana;
      const targetDay = overPlato.dia_semana;

      if (sourceDay === targetDay) {
        // Reordenar dentro del mismo día
        const dayPlatos = platosPorDia(sourceDay);
        const oldIndex = dayPlatos.findIndex(p => p.id === activeId);
        const newIndex = dayPlatos.findIndex(p => p.id === overId);

        if (oldIndex !== newIndex) {
          const newOrder = [...dayPlatos];
          const [removed] = newOrder.splice(oldIndex, 1);
          newOrder.splice(newIndex, 0, removed);
          reordenarPlatos(newOrder, sourceDay);
        }
      } else {
        // Mover entre días diferentes
        const targetDayPlatos = platosPorDia(targetDay);
        const newIndex = targetDayPlatos.findIndex(p => p.id === overId);
        moverPlatoEntreDias(activeId, sourceDay, targetDay, newIndex);
      }
    }
  };

  // Renderiza los platos de un día específico - Optimizado para móvil
  const renderDiaColumn = (dia: { id: number; nombre: string }) => {
    const platosDia = platosPorDia(dia.id === 0 ? null : dia.id);
    const diaId = dia.id === 0 ? null : dia.id;
    const isSinAsignar = dia.id === 0;

    return (
      <DroppableColumn
        id={`day-${dia.id}`}
        className="flex-shrink-0 w-72 md:w-80 snap-start"
      >
        <Card
          className={`h-full shadow-lg border-0 rounded-xl ${
            isSinAsignar
              ? 'bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600'
              : 'bg-white dark:bg-gray-900'
          }`}
        >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className={`text-lg font-bold ${
                isSinAsignar
                  ? 'text-gray-600 dark:text-gray-400'
                  : 'text-gray-900 dark:text-white'
              }`}>
                {isSinAsignar ? '📦 ' : '📅 '}{dia.nombre}
              </CardTitle>
              <Badge
                variant={isSinAsignar ? "outline" : "secondary"}
                className="text-xs"
              >
                {platosDia.length}
              </Badge>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleNewDish(diaId)}
              className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20 touch-target"
            >
              <Plus className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </Button>
          </div>
          {isSinAsignar && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Arrastra aquí los platos sin día asignado
            </p>
          )}
        </CardHeader>
        <CardContent className="pt-0 pb-4 px-4">
          <div className="space-y-2 max-h-96 overflow-y-auto min-h-[100px]">
            <SortableContext
              items={platosDia.map(p => p.id)}
              strategy={verticalListSortingStrategy}
            >
              {platosDia.map((plato) => (
                <SortableDishItem
                  key={plato.id}
                  plato={plato}
                  onEdit={handleEditDish}
                />
              ))}
            </SortableContext>
            {platosDia.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                {isSinAsignar ? (
                  <>
                    <div className="h-8 w-8 mx-auto mb-2 opacity-50 text-2xl">📦</div>
                    <p className="text-sm">Zona de platos sin asignar</p>
                    <p className="text-xs mt-1 opacity-75">
                      Arrastra platos aquí para quitarlos del menú semanal
                    </p>
                  </>
                ) : (
                  <>
                    <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No hay platos para este día</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleNewDish(diaId)}
                      className="mt-2"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Agregar plato
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
        </CardContent>
        </Card>
      </DroppableColumn>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
      {/* Header */}
      <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 sticky top-0 z-40">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                Admin Menú
              </h1>
              <Badge variant="outline" className="hidden sm:inline-flex">
                {platos.length} platos
              </Badge>
            </div>

            {/* Desktop Actions */}
            <div className="hidden md:flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Recargar
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link to="/">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Volver
                </Link>
              </Button>
              <Button variant="destructive" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Salir
              </Button>
            </div>

            {/* Mobile Menu */}
            <div className="md:hidden">
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-64">
                  <SheetHeader>
                    <SheetTitle>Menú</SheetTitle>
                  </SheetHeader>
                  <div className="flex flex-col gap-2 mt-6">
                    <Button variant="outline" onClick={handleRefresh} className="justify-start">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Recargar
                    </Button>
                    <Button variant="outline" asChild className="justify-start">
                      <Link to="/">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Volver al sitio
                      </Link>
                    </Button>
                    <Button variant="destructive" onClick={handleLogout} className="justify-start">
                      <LogOut className="h-4 w-4 mr-2" />
                      Salir
                    </Button>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </header>
      {/* Main Content */}
      <main className="flex-1">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          {/* Tab Navigation */}
          <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
            <div className="px-4 sm:px-6 lg:px-8">
              <TabsList className="grid w-full grid-cols-2 max-w-md">
                <TabsTrigger value="menu" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span className="hidden sm:inline">Menú Semanal</span>
                  <span className="sm:hidden">Menú</span>
                </TabsTrigger>
                <TabsTrigger value="platos" className="flex items-center gap-2">
                  <List className="h-4 w-4" />
                  <span className="hidden sm:inline">Todos los Platos</span>
                  <span className="sm:hidden">Platos</span>
                </TabsTrigger>
              </TabsList>
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-4 sm:p-6 lg:p-8 pb-20 md:pb-8">
            <TabsContent value="menu" className="mt-0">
              <div className="mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Menú Semanal
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                      Arrastra los platos para organizarlos por día
                    </p>
                  </div>
                  <Button onClick={() => handleNewDish()} className="self-start sm:self-auto hidden md:flex">
                    <Plus className="h-4 w-4 mr-2" />
                    Nuevo Plato
                  </Button>
                </div>
              </div>

              {/* Días de la semana - Scroll horizontal en móvil */}
              <DndContext
                sensors={sensors}
                collisionDetection={closestCorners}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDragEnd={handleDragEnd}
              >
                <div className="flex gap-4 overflow-x-auto pb-4 snap-x snap-mandatory scroll-smooth touch-manipulation">
                  {diasSemana.map(renderDiaColumn)}
                </div>
                <DragOverlay>
                  {activeDragId ? (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700 shadow-lg opacity-90">
                      <div className="font-medium text-sm">
                        {platos.find(p => p.id === activeDragId)?.nombre}
                      </div>
                    </div>
                  ) : null}
                </DragOverlay>
              </DndContext>
            </TabsContent>

            <TabsContent value="platos" className="mt-0">
              <div className="mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Todos los Platos
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                      Gestiona todos los platos del menú
                    </p>
                  </div>
                </div>
              </div>
              <DishList />
            </TabsContent>
          </div>
        </Tabs>
      </main>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav
        activeTab={activeTab}
        onTabChange={setActiveTab}
        onNewDish={() => handleNewDish()}
        onMenuOpen={() => setIsMobileMenuOpen(true)}
      />

      {/* Dialog para crear/editar platos */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingDish ? 'Editar Plato' : 'Nuevo Plato'}
            </DialogTitle>
          </DialogHeader>
          <DishForm
            plato={editingDish}
            onSuccess={handleCloseDialog}
            defaultDiaSemanaa={selectedDia}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};
