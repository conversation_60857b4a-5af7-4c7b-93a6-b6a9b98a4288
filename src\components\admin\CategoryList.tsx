import React, { useState, useEffect } from 'react';
import { useDragDrop } from './DragDropContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, X } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { PlatoMenu } from '@/lib/types';
import { platosMenuApi } from '@/lib/api';

export const CategoryList: React.FC = () => {
  const { platos, cargarDatos } = useDragDrop();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const { toast } = useToast();

  // Extraer categorías únicas de los platos
  useEffect(() => {
    const uniqueCategories = new Set<string>();
    platos.forEach(plato => {
      plato.categorias?.forEach(cat => uniqueCategories.add(cat));
    });
    setCategories(Array.from(uniqueCategories).sort());
  }, [platos]);

  const handleAddCategory = async () => {
    const category = newCategory.trim();
    if (!category) return;

    if (categories.includes(category)) {
      toast({
        title: 'Categoría duplicada',
        description: 'Esta categoría ya existe',
        variant: 'destructive',
      });
      return;
    }

    setCategories([...categories, category]);
    setNewCategory('');
    setIsDialogOpen(false);
  };

  const handleDeleteCategory = async (categoryToDelete: string) => {
    try {
      // Actualizar todos los platos que tienen esta categoría
      const platosToUpdate = platos.filter(plato => 
        plato.categorias?.includes(categoryToDelete)
      );

      for (const plato of platosToUpdate) {
        await platosMenuApi.update(plato.id, {
          ...plato,
          categorias: plato.categorias?.filter(cat => cat !== categoryToDelete) || []
        });
      }

      await cargarDatos(true);
      toast({
        title: 'Categoría eliminada',
        description: 'La categoría ha sido eliminada correctamente',
      });
    } catch (error) {
      console.error('Error al eliminar categoría:', error);
      toast({
        title: 'Error',
        description: 'No se pudo eliminar la categoría',
        variant: 'destructive',
      });
    }
  };

  const getUsageCount = (category: string): number => {
    return platos.filter(plato => plato.categorias?.includes(category)).length;
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Categorías / Etiquetas</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Nueva Categoría
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Nueva Categoría</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder="Nombre de la categoría"
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddCategory();
                    }
                  }}
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={handleAddCategory}>
                  Agregar
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Badge
              key={category}
              variant="secondary"
              className="flex items-center gap-2 p-2"
            >
              {category}
              <span className="text-xs text-gray-500">
                ({getUsageCount(category)})
              </span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => handleDeleteCategory(category)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          {categories.length === 0 && (
            <p className="text-sm text-gray-500">
              No hay categorías definidas. Crea una nueva categoría para empezar.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
