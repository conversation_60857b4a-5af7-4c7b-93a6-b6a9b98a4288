import { PlatoMenu, PlatoMenuForm } from "./types";
import { supabase, supabaseAdmin } from './supabase';

// API para platos de menú
export const platosMenuApi = {
  // Obtener todos los platos
  getAll: async (): Promise<PlatoMenu[]> => {
    const { data, error } = await supabase
      .from('platos_menu')
      .select('*')
      .order('orden_visualizacion', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Obtener platos por día de la semana
  getByDia: async (dia: number | null): Promise<PlatoMenu[]> => {
    const query = supabase
      .from('platos_menu')
      .select('*')
      .order('orden_visualizacion', { ascending: true });
    if (dia === null) {
      query.is('dia_semana', null);
    } else {
      query.eq('dia_semana', dia);
    }
    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  },

  // Obtener un plato por ID
  getById: async (id: number): Promise<PlatoMenu> => {
    const { data, error } = await supabase
      .from('platos_menu')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Crear un nuevo plato
  create: async (plato: PlatoMenuForm): Promise<PlatoMenu> => {
    // Obtener el máximo orden_visualizacion actual para el día (o sin día)
    const platos = await platosMenuApi.getByDia(plato.dia_semana ?? null);
    const maxOrden = platos.length > 0
      ? Math.max(...platos.map(p => p.orden_visualizacion))
      : 0;

    const nuevoPlato = {
      ...plato,
      orden_visualizacion: maxOrden + 1,
    };

    const { data, error } = await supabaseAdmin
      .from('platos_menu')
      .insert(nuevoPlato)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  // Actualizar un plato existente
  update: async (id: number, plato: Partial<PlatoMenu>): Promise<PlatoMenu> => {
    const { data, error } = await supabaseAdmin
      .from('platos_menu')
      .update(plato)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Eliminar un plato
  delete: async (id: number): Promise<void> => {
    const { error } = await supabaseAdmin
      .from('platos_menu')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // Actualizar el orden de visualización de varios platos
  updateOrden: async (platos: { id: number; orden_visualizacion: number; dia_semana?: number | null }[]): Promise<void> => {
    // Usar transacciones para actualizar múltiples registros
    const promises = platos.map(plato =>
      platosMenuApi.update(plato.id, {
        orden_visualizacion: plato.orden_visualizacion,
        dia_semana: plato.dia_semana ?? null
      })
    );
    await Promise.all(promises);
  },
};
