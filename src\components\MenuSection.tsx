
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useMenuData } from "@/hooks/use-menu-data";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

const MenuSection = () => {
  const { dias, platosPorDia, isLoading, error } = useMenuData();
  const [activeTab, setActiveTab] = useState<string>("");

  // Establecer el primer día como activo cuando se cargan los datos
  if (dias.length > 0 && activeTab === "") {
    setActiveTab(dias[0].id.toString());
  }

  return (
    <section id="menú" className="py-20 bg-gray-50 relative overflow-hidden">
      {/* Decorative elements with animation */}
      <div className="absolute -bottom-24 -left-24 w-64 h-64 rounded-full bg-cantina-100 opacity-50 animate-float animation-delay-200"></div>
      <div className="absolute top-32 -right-24 w-48 h-48 rounded-full bg-cantina-200 opacity-40 animate-float animation-delay-300"></div>
      <div className="absolute top-96 left-12 w-32 h-32 rounded-full bg-cantina-300 opacity-30 animate-float animation-delay-400"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl font-pearly text-gray-900 mb-4">Nuestro Menú</h2>
          <div className="w-24 h-1 bg-cantina-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Descubre nuestra selección de platillos preparados con los mejores ingredientes
            y recetas auténticas tradicionales.
          </p>
        </div>

        {/* Error state */}
        {error && (
          <Alert variant="destructive" className="mb-8">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              No pudimos cargar el menú. Por favor, intenta de nuevo más tarde.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading state */}
        {isLoading ? (
          <div className="space-y-8">
            <div className="flex justify-center space-x-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <Skeleton key={i} className="h-10 w-24 rounded-full" />
              ))}
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-40 w-full rounded-lg" />
              ))}
            </div>
          </div>
        ) : (
          <>
            {/* Menu Dias de la Semana as Tabs */}
            {dias.length > 0 && (
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="flex flex-wrap justify-center mb-8 bg-transparent h-auto">
                  {dias.map((dia) => (
                    <TabsTrigger
                      key={dia.id}
                      value={dia.id.toString()}
                      className={cn(
                        "px-6 py-2 rounded-full text-lg font-pearly transition-all transform hover:scale-105 data-[state=active]:bg-cantina-600 data-[state=active]:text-white data-[state=active]:shadow-md",
                        "data-[state=inactive]:bg-white data-[state=inactive]:text-gray-700 data-[state=inactive]:hover:bg-cantina-100"
                      )}
                    >
                      {dia.nombre}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {/* Menu Items for each dia */}
                {dias.map((dia) => (
                  <TabsContent
                    key={dia.id}
                    value={dia.id.toString()}
                    className="mt-0"
                  >
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={dia.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className="grid md:grid-cols-2 gap-8"
                      >
                        {platosPorDia(dia.id).length > 0 ? (
                          platosPorDia(dia.id).map((plato, index) => (
                            <motion.div
                              key={plato.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.1 }}
                              className={cn(
                                "bg-white rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-xl transform hover:scale-102",
                                plato.destacado && "border-l-4 border-cantina-500"
                              )}
                            >
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="flex items-center">
                                    <h3 className="text-xl font-pearly text-gray-800">{plato.nombre}</h3>
                                    {plato.destacado && (
                                      <span className="ml-3 px-2 py-1 bg-cantina-100 text-cantina-800 text-xs rounded-full animate-pulse">
                                        Destacado
                                      </span>
                                    )}
                                  </div>
                                  <p className="text-gray-600 mt-2">{plato.descripcion || "Sin descripción"}</p>                                  <div className="mt-2 flex flex-wrap gap-2">
                                    <span className="text-sm font-medium text-cantina-600 bg-cantina-50 px-2 py-1 rounded">
                                      {plato.tipo}
                                    </span>

                                    {/* Etiquetas del plato */}
                                    {plato.categorias?.map(categoria => (
                                      <span key={categoria} className="text-sm font-medium text-indigo-600 bg-indigo-50 px-2 py-1 rounded">
                                        {categoria}
                                      </span>
                                    ))}

                                    {/* Etiquetas dietéticas */}
                                    {plato.sin_gluten && (
                                      <span className="text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded">
                                        Sin Gluten
                                      </span>
                                    )}

                                    {plato.sin_lactosa && (
                                      <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                        Sin Lactosa
                                      </span>
                                    )}

                                    {plato.vegetariano && (
                                      <span className="text-sm font-medium text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                        Vegetariano
                                      </span>
                                    )}

                                    {plato.vegano && (
                                      <span className="text-sm font-medium text-emerald-600 bg-emerald-50 px-2 py-1 rounded">
                                        Vegano
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </motion.div>
                          ))
                        ) : (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="col-span-2 text-center py-12 bg-white rounded-lg shadow-md"
                          >
                            <p className="text-gray-500 text-lg">No hay platos disponibles para este día.</p>
                          </motion.div>
                        )}
                      </motion.div>
                    </AnimatePresence>
                  </TabsContent>
                ))}
              </Tabs>
            )}
          </>
        )}
      </div>
    </section>
  );
};

export default MenuSection;
