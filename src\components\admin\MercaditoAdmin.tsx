import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Plus, Edit2, Trash2, ShoppingBag, Calendar, CalendarDays, Power, PowerOff, Loader2 } from 'lucide-react';
import { useMercadito } from '@/hooks/use-mercadito';
import { MercaditoItem } from '@/lib/mercadito-api';

// Map categories to match database structure
const categoryMap = {
  'Empanadas': 'artesanales',
  'Dulces': 'artesanales',
  'Postres': 'artesanales',
  'Bebidas': 'bebidas',
  'Frescos': 'frescos',
} as const;

interface ProductFormProps {
  product?: MercaditoItem | null;
  onSuccess: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onSuccess }) => {
  const [formData, setFormData] = useState({
    nombre: product?.nombre || '',
    descripcion: product?.descripcion || '',
    precio: product?.precio || 0,
    disponible: product?.disponible || false,
    categoria: product?.categoria || 'Empanadas',
    orden_visualizacion: product?.orden_visualizacion || 0,
  });
  const { createItem, updateItem } = useMercadito();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (product) {
        await updateItem(product.id, formData);
      } else {
        await createItem(formData);
      }

      onSuccess();
    } catch (error) {
      // Error handling is done in the hook
      console.error('Error saving product:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="nombre">Nombre del producto</Label>
        <Input
          id="nombre"
          value={formData.nombre}
          onChange={(e) => setFormData({ ...formData, nombre: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="descripcion">Descripción (opcional)</Label>
        <Textarea
          id="descripcion"
          value={formData.descripcion}
          onChange={(e) => setFormData({ ...formData, descripcion: e.target.value })}
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="precio">Precio ($UY)</Label>
        <Input
          id="precio"
          type="number"
          value={formData.precio}
          onChange={(e) => setFormData({ ...formData, precio: Number(e.target.value) })}
          min="0"
          step="10"
        />
      </div>

      <div>
        <Label htmlFor="categoria">Categoría</Label>
        <select
          id="categoria"
          value={formData.categoria}
          onChange={(e) => setFormData({ ...formData, categoria: e.target.value })}
          className="w-full p-2 border border-gray-300 rounded-md"
        >
          <option value="Empanadas">Empanadas</option>
          <option value="Dulces">Dulces</option>
          <option value="Postres">Postres</option>
          <option value="Bebidas">Bebidas</option>
          <option value="Frescos">Frescos</option>
        </select>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="disponible"
          checked={formData.disponible}
          onCheckedChange={(checked) => setFormData({ ...formData, disponible: checked })}
        />
        <Label htmlFor="disponible">Disponible hoy</Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="submit">
          {product ? 'Actualizar' : 'Crear'} Producto
        </Button>
      </div>
    </form>
  );
};

interface ProductCardProps {
  product: MercaditoItem;
  onEdit: (product: MercaditoItem) => void;
  onDelete: (id: number) => void;
  onToggleAvailable: (id: number, available: boolean) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onEdit, onDelete, onToggleAvailable }) => {
  const getCategoryIcon = (categoria: string) => {
    switch (categoria) {
      case 'Frescos':
        return '🥬';
      case 'Empanadas':
      case 'Dulces':
      case 'Postres':
        return '🥖';
      case 'Bebidas':
        return '☕';
      default:
        return '📦';
    }
  };

  const getCategoryColor = (categoria: string) => {
    switch (categoria) {
      case 'Frescos':
        return 'bg-green-100 text-green-800';
      case 'Empanadas':
      case 'Dulces':
      case 'Postres':
        return 'bg-orange-100 text-orange-800';
      case 'Bebidas':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-medium text-gray-900 dark:text-white">{product.nombre}</h3>
              {product.disponible && (
                <Badge className="bg-green-100 text-green-800 text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  Hoy
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2 mb-2">
              <Badge className={`text-xs ${getCategoryColor(product.categoria)}`}>
                {getCategoryIcon(product.categoria)} {product.categoria}
              </Badge>
              {product.precio && (
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  ${product.precio}
                </span>
              )}
            </div>

            {product.descripcion && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                {product.descripcion}
              </p>
            )}
          </div>

          <div className="flex gap-1 ml-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onToggleAvailable(product.id, !product.disponible)}
              className="h-8 w-8 p-0"
              title={product.disponible ? "Quitar de hoy" : "Marcar disponible hoy"}
            >
              <Calendar className={`h-3 w-3 ${product.disponible ? 'text-green-600' : 'text-gray-400'}`} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onEdit(product)}
              className="h-8 w-8 p-0"
            >
              <Edit2 className="h-3 w-3" />
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>¿Eliminar producto?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Esta acción eliminará "{product.nombre}" y no se puede deshacer.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction onClick={() => onDelete(product.id)}>
                    Eliminar
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const MercaditoAdmin: React.FC = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<MercaditoItem | null>(null);
  const [filter, setFilter] = useState<'all' | 'today'>('all');
  const [isDateDialogOpen, setIsDateDialogOpen] = useState(false);
  const [customDateInput, setCustomDateInput] = useState<string>('');

  const {
    items,
    settings,
    isLoading,
    availableItems,
    isEnabled,
    currentDate,
    deleteItem,
    toggleItemAvailability,
    toggleMercadito,
    disableAllItems,
    setCustomDate,
  } = useMercadito();

  const filteredProducts = filter === 'today'
    ? items.filter(p => p.disponible && isEnabled)
    : items;

  const handleEdit = (product: MercaditoItem) => {
    setEditingProduct(product);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteItem(id);
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  };

  const handleToggleAvailable = async (id: number, available: boolean) => {
    try {
      await toggleItemAvailability(id, available);
    } catch (error) {
      console.error('Error toggling availability:', error);
    }
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingProduct(null);
  };

  const handleToggleMercadito = async () => {
    try {
      await toggleMercadito();
    } catch (error) {
      console.error('Error toggling mercadito:', error);
    }
  };

  const handleDisableAllProducts = async () => {
    try {
      await disableAllItems();
    } catch (error) {
      console.error('Error disabling all products:', error);
    }
  };

  const handleDateChange = async () => {
    try {
      console.log('Cambiando fecha a:', customDateInput);
      await setCustomDate(customDateInput || null);
      setIsDateDialogOpen(false);
      setCustomDateInput(''); // Limpiar el input después de aplicar
    } catch (error) {
      console.error('Error setting custom date:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                🛒 Mercadito
              </h2>
              <div className="flex items-center gap-2">
                <Button
                  variant={isEnabled ? "default" : "destructive"}
                  size="sm"
                  onClick={handleToggleMercadito}
                  className="flex items-center gap-2"
                  disabled={isLoading}
                >
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : isEnabled ? <Power className="h-4 w-4" /> : <PowerOff className="h-4 w-4" />}
                  {isEnabled ? 'Activo' : 'Inactivo'}
                </Button>
              </div>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Gestiona los productos del mercadito
            </p>
            <div className="mt-2 flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                📅 Fecha configurada:
              </span>
              <span className="text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                {currentDate}
              </span>
              {settings?.custom_date && (
                <span className="text-xs text-orange-600 dark:text-orange-400">
                  (Fecha personalizada)
                </span>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Dialog open={isDateDialogOpen} onOpenChange={setIsDateDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <CalendarDays className="h-4 w-4 mr-2" />
                  Cambiar Fecha
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Cambiar Fecha del Mercadito</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      📅 <strong>Fecha actual:</strong> {currentDate}
                    </p>
                    {settings?.custom_date && (
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        Esta es una fecha personalizada
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="custom-date">Nueva fecha para el mercadito</Label>
                    <Input
                      id="custom-date"
                      type="date"
                      value={customDateInput}
                      onChange={(e) => setCustomDateInput(e.target.value)}
                      className="mt-1"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Selecciona una fecha específica para el mercadito
                    </p>
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setCustomDateInput('')}>
                      Usar Hoy
                    </Button>
                    <Button
                      onClick={handleDateChange}
                      disabled={!customDateInput}
                    >
                      Aplicar Fecha
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Nuevo Producto
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>
                    {editingProduct ? 'Editar Producto' : 'Nuevo Producto'}
                  </DialogTitle>
                </DialogHeader>
                <ProductForm
                  product={editingProduct}
                  onSuccess={handleCloseDialog}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDisableAllProducts}
            className="text-red-600 hover:text-red-700"
          >
            <PowerOff className="h-4 w-4 mr-2" />
            Deshabilitar Todo
          </Button>
        </div>
      </div>

      {/* Available Today Section */}
      {isEnabled ? (
        availableItems.length > 0 ? (
          <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-800">
                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                Disponible Hoy
                <Badge variant="secondary">{availableItems.length}</Badge>
              </CardTitle>
              <p className="text-sm text-orange-700">
                {currentDate}
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {availableItems.map((product) => (
                  <div key={product.id} className="bg-white rounded-lg p-3 border border-orange-200">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="font-medium text-gray-900">{product.nombre}</span>
                      {product.precio && (
                        <span className="text-sm text-gray-600 ml-auto">${product.precio}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200">
            <CardContent className="p-6 text-center">
              <div className="text-gray-500">
                <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="font-medium">No hay productos disponibles hoy</p>
                <p className="text-sm mt-1">{currentDate}</p>
              </div>
            </CardContent>
          </Card>
        )
      ) : (
        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardContent className="p-6 text-center">
            <div className="text-red-600">
              <PowerOff className="h-8 w-8 mx-auto mb-2" />
              <p className="font-medium">Mercadito Deshabilitado</p>
              <p className="text-sm mt-1">El mercadito no aparecerá en el frontend</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filter Buttons */}
      <div className="flex gap-2">
        <Button
          variant={filter === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilter('all')}
          disabled={isLoading}
        >
          Todos ({items.length})
        </Button>
        <Button
          variant={filter === 'today' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilter('today')}
          disabled={isLoading}
        >
          Disponibles Hoy ({availableItems.length})
        </Button>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="text-center py-12">
          <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin text-gray-500" />
          <p className="text-gray-500">Cargando productos del mercadito...</p>
        </div>
      ) : (
        <>
          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onToggleAvailable={handleToggleAvailable}
              />
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-12 text-gray-500 dark:text-gray-400">
              <ShoppingBag className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">
                {filter === 'today' ? 'No hay productos disponibles hoy' : 'No hay productos'}
              </h3>
              <p className="text-sm mb-4">
                {filter === 'today'
                  ? 'Marca algunos productos como disponibles para hoy'
                  : 'Crea tu primer producto para comenzar'
                }
              </p>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Crear Producto
                  </Button>
                </DialogTrigger>
              </Dialog>
            </div>
          )}
        </>
      )}
    </div>
  );
};
