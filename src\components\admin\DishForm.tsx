import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { PlatoMenu, PlatoMenuForm } from '@/lib/types';
import { platosMenuApi } from '@/lib/api';
import { useDragDrop } from './DragDropContext';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

// Esquema de validación para el formulario
const formSchema = z.object({
  nombre: z.string().min(1, 'El nombre es requerido'),
  descripcion: z.string().optional(),
  tipo: z.enum(['principal', 'guarnicion', 'postre'], {
    required_error: 'Debes seleccionar un tipo de plato',
  }),
  destacado: z.boolean().default(false),
  sin_gluten: z.boolean().default(false),
  sin_lactosa: z.boolean().default(false),
  vegetariano: z.boolean().default(false),
  vegano: z.boolean().default(false),
  dia_semana: z.number().min(1).max(5).optional().nullable(),
  categorias: z.array(z.string()).optional(),
});

interface DishFormProps {
  plato: PlatoMenu | null;
  onSuccess: () => void;
  defaultDiaSemanaa?: number | null;
}

export const DishForm: React.FC<DishFormProps> = ({ plato, onSuccess, defaultDiaSemanaa }) => {
  const { cargarDatos } = useDragDrop();
  const { toast } = useToast();

  // Configurar el formulario con react-hook-form y zod
  const form = useForm<PlatoMenuForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nombre: plato?.nombre || '',
      descripcion: plato?.descripcion || '',
      tipo: plato?.tipo || 'principal',
      destacado: plato?.destacado || false,
      sin_gluten: plato?.sin_gluten || false,
      sin_lactosa: plato?.sin_lactosa || false,
      vegetariano: plato?.vegetariano || false,
      vegano: plato?.vegano || false,
      dia_semana: plato?.dia_semana || defaultDiaSemanaa || null,
      categorias: plato?.categorias || [],
    },
  });

  const onSubmit = async (data: PlatoMenuForm) => {
    try {
      if (plato) {
        // Actualizar plato existente
        const updatedPlato = await platosMenuApi.update(plato.id, data);
        if (!updatedPlato) {
          throw new Error('No se pudo actualizar el plato');
        }
        toast({
          title: 'Plato actualizado',
          description: 'El plato ha sido actualizado correctamente',
        });
      } else {
        // Crear nuevo plato
        const newPlato = await platosMenuApi.create(data);
        if (!newPlato) {
          throw new Error('No se pudo crear el plato');
        }
        toast({
          title: 'Plato creado',
          description: 'El plato ha sido creado correctamente',
        });
      }
      await cargarDatos(true);
      onSuccess();
    } catch (error) {
      console.error('Error al guardar plato:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      // Check for auth-related errors
      if (errorMessage.includes('401') || errorMessage.toLowerCase().includes('unauthorized')) {
        toast({
          title: 'Error de autenticación',
          description: 'Tu sesión ha expirado. Por favor, vuelve a iniciar sesión.',
          variant: 'destructive',
        });
        // Clear auth and redirect to login
        localStorage.removeItem('admin_authenticated');
        window.location.reload();
        return;
      }

      toast({
        title: 'Error',
        description: `No se pudo guardar el plato: ${errorMessage}`,
        variant: 'destructive',
      });
    }
  };

  // Componente para ingresar etiquetas/categorías - Optimizado para móvil
  const CategoriasInput: React.FC = () => {
    const categorias = form.watch('categorias') || [];
    const [input, setInput] = React.useState('');

    const addCategoria = () => {
      if (input.trim() && !categorias.includes(input.trim())) {
        form.setValue('categorias', [...categorias, input.trim()]);
        setInput('');
      }
    };

    const removeCategoria = (cat: string) => {
      form.setValue('categorias', categorias.filter((c: string) => c !== cat));
    };

    return (
      <div className="space-y-3">
        <FormLabel className="text-sm font-medium">🏷️ Categorías / Etiquetas</FormLabel>

        {/* Etiquetas existentes */}
        {categorias.length > 0 && (
          <div className="flex gap-2 flex-wrap">
            {categorias.map((cat: string) => (
              <span
                key={cat}
                className="inline-flex items-center gap-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full px-3 py-1 text-xs font-medium"
              >
                {cat}
                <button
                  type="button"
                  onClick={() => removeCategoria(cat)}
                  className="ml-1 text-blue-600 dark:text-blue-400 hover:text-red-500 transition-colors"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        )}

        {/* Input para nueva etiqueta */}
        <div className="flex gap-2">
          <Input
            value={input}
            onChange={e => setInput(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addCategoria();
              }
            }}
            placeholder="Ej: picante, nuevo, especial..."
            className="h-11"
          />
          <Button
            type="button"
            onClick={addCategoria}
            variant="outline"
            className="px-4"
          >
            ➕
          </Button>
        </div>

        <FormDescription className="text-xs">
          Agrega etiquetas para categorizar el plato (ej: "picante", "nuevo", "especial del día")
        </FormDescription>
      </div>
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Información básica */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Información básica
          </h3>

          <FormField
            control={form.control}
            name="nombre"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">Nombre del plato</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Ej: Milanesa con puré"
                    {...field}
                    className="h-11"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="descripcion"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">Descripción</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Ej: Milanesa de carne con puré de papas casero"
                    {...field}
                    value={field.value || ''}
                    className="min-h-[80px] resize-none"
                  />
                </FormControl>
                <FormDescription className="text-xs">
                  Una breve descripción del plato (opcional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="tipo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Tipo de plato</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Selecciona un tipo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="principal">🍽️ Principal</SelectItem>
                      <SelectItem value="guarnicion">🥗 Guarnición</SelectItem>
                      <SelectItem value="postre">🍰 Postre</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dia_semana"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Día de la semana</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(value ? Number(value) : null)}
                    value={field.value?.toString() ?? undefined}
                    defaultValue={field.value?.toString() ?? undefined}
                  >
                    <FormControl>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Día (opcional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="0">Sin asignar</SelectItem>
                      <SelectItem value="1">📅 Lunes</SelectItem>
                      <SelectItem value="2">📅 Martes</SelectItem>
                      <SelectItem value="3">📅 Miércoles</SelectItem>
                      <SelectItem value="4">📅 Jueves</SelectItem>
                      <SelectItem value="5">📅 Viernes</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Opciones dietéticas */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Opciones dietéticas
          </h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <FormField
              control={form.control}
              name="sin_gluten"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800/50">
                  <div className="space-y-0.5">
                    <FormLabel className="text-sm font-medium">🌾 Sin Gluten</FormLabel>
                    <FormDescription className="text-xs">
                      No contiene gluten
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sin_lactosa"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800/50">
                  <div className="space-y-0.5">
                    <FormLabel className="text-sm font-medium">🥛 Sin Lactosa</FormLabel>
                    <FormDescription className="text-xs">
                      No contiene lactosa
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="vegetariano"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800/50">
                  <div className="space-y-0.5">
                    <FormLabel className="text-sm font-medium">🌱 Vegetariano</FormLabel>
                    <FormDescription className="text-xs">
                      Apto para vegetarianos
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="vegano"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800/50">
                  <div className="space-y-0.5">
                    <FormLabel className="text-sm font-medium">🌿 Vegano</FormLabel>
                    <FormDescription className="text-xs">
                      Apto para veganos
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Destacado y categorías */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Configuración adicional
          </h3>

          <FormField
            control={form.control}
            name="destacado"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border border-yellow-200 dark:border-yellow-800 p-4 bg-yellow-50 dark:bg-yellow-900/20">
                <div className="space-y-0.5">
                  <FormLabel className="text-sm font-medium">⭐ Destacado</FormLabel>
                  <FormDescription className="text-xs">
                    Marcar este plato como destacado en el menú
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* Campo de etiquetas */}
          <FormField
            control={form.control}
            name="categorias"
            render={() => (
              <FormItem>
                <CategoriasInput />
              </FormItem>
            )}
          />
        </div>

        {/* Botones de acción */}
        <div className="flex flex-col-reverse sm:flex-row justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onSuccess}
            className="w-full sm:w-auto"
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            className="w-full sm:w-auto"
          >
            {plato ? '✏️ Actualizar Plato' : '➕ Crear Plato'}
          </Button>
        </div>
      </form>
    </Form>
  );
};
