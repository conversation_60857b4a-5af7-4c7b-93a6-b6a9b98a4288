import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Types for mercadito products
export interface ProductoMercadito {
  id: number;
  nombre: string;
  descripcion?: string;
  precio?: number;
  disponible_hoy: boolean;
  categoria: 'frescos' | 'artesanales' | 'bebidas' | 'otros';
  imagen_url?: string;
  created_at: string;
  updated_at: string;
}

// Mercadito settings
export interface MercaditoSettings {
  enabled: boolean;
  customDate: string;
  lastUpdated: string;
}

// Context type
interface MercaditoContextType {
  // Products
  products: ProductoMercadito[];
  setProducts: (products: ProductoMercadito[]) => void;
  addProduct: (product: Omit<ProductoMercadito, 'id' | 'created_at' | 'updated_at'>) => void;
  updateProduct: (id: number, updates: Partial<ProductoMercadito>) => void;
  deleteProduct: (id: number) => void;
  
  // Settings
  settings: MercaditoSettings;
  updateSettings: (updates: Partial<MercaditoSettings>) => void;
  
  // Computed values
  availableProducts: ProductoMercadito[];
  isEnabled: boolean;
  currentDate: string;
  
  // Actions
  toggleMercadito: () => void;
  disableAllProducts: () => void;
  toggleProductAvailability: (id: number, available: boolean) => void;
}

const MercaditoContext = createContext<MercaditoContextType | undefined>(undefined);

// Default products
const defaultProducts: ProductoMercadito[] = [
  {
    id: 1,
    nombre: 'Baba Ganoush casero',
    descripcion: 'Deliciosa pasta de berenjenas con tahini y especias',
    precio: 350,
    disponible_hoy: true,
    categoria: 'artesanales',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 2,
    nombre: 'Focaccia con romero y aceite de oliva',
    descripcion: 'Pan artesanal con hierbas frescas',
    precio: 280,
    disponible_hoy: true,
    categoria: 'artesanales',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 3,
    nombre: 'Pancitos artesanales',
    descripcion: 'Variedad de panes caseros',
    precio: 150,
    disponible_hoy: true,
    categoria: 'artesanales',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

// Default settings
const defaultSettings: MercaditoSettings = {
  enabled: true,
  customDate: '',
  lastUpdated: new Date().toISOString(),
};

// Storage keys
const STORAGE_KEYS = {
  PRODUCTS: 'mercadito_products',
  SETTINGS: 'mercadito_settings',
};

// Provider component
export const MercaditoProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [products, setProductsState] = useState<ProductoMercadito[]>([]);
  const [settings, setSettingsState] = useState<MercaditoSettings>(defaultSettings);

  // Load data from localStorage on mount
  useEffect(() => {
    try {
      const savedProducts = localStorage.getItem(STORAGE_KEYS.PRODUCTS);
      const savedSettings = localStorage.getItem(STORAGE_KEYS.SETTINGS);

      if (savedProducts) {
        setProductsState(JSON.parse(savedProducts));
      } else {
        setProductsState(defaultProducts);
      }

      if (savedSettings) {
        setSettingsState(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading mercadito data:', error);
      setProductsState(defaultProducts);
      setSettingsState(defaultSettings);
    }
  }, []);

  // Save products to localStorage whenever they change
  const setProducts = (newProducts: ProductoMercadito[]) => {
    setProductsState(newProducts);
    localStorage.setItem(STORAGE_KEYS.PRODUCTS, JSON.stringify(newProducts));
  };

  // Save settings to localStorage whenever they change
  const updateSettings = (updates: Partial<MercaditoSettings>) => {
    const newSettings = {
      ...settings,
      ...updates,
      lastUpdated: new Date().toISOString(),
    };
    setSettingsState(newSettings);
    localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(newSettings));
  };

  // Product management functions
  const addProduct = (productData: Omit<ProductoMercadito, 'id' | 'created_at' | 'updated_at'>) => {
    const newProduct: ProductoMercadito = {
      ...productData,
      id: Math.max(0, ...products.map(p => p.id)) + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    setProducts([...products, newProduct]);
  };

  const updateProduct = (id: number, updates: Partial<ProductoMercadito>) => {
    const updatedProducts = products.map(product =>
      product.id === id
        ? { ...product, ...updates, updated_at: new Date().toISOString() }
        : product
    );
    setProducts(updatedProducts);
  };

  const deleteProduct = (id: number) => {
    const filteredProducts = products.filter(product => product.id !== id);
    setProducts(filteredProducts);
  };

  const toggleProductAvailability = (id: number, available: boolean) => {
    updateProduct(id, { disponible_hoy: available });
  };

  // Settings management functions
  const toggleMercadito = () => {
    updateSettings({ enabled: !settings.enabled });
  };

  const disableAllProducts = () => {
    const updatedProducts = products.map(product => ({
      ...product,
      disponible_hoy: false,
      updated_at: new Date().toISOString(),
    }));
    setProducts(updatedProducts);
  };

  // Computed values
  const availableProducts = products.filter(p => p.disponible_hoy && settings.enabled);
  const isEnabled = settings.enabled;

  const getCurrentDate = () => {
    const dateToUse = settings.customDate ? new Date(settings.customDate) : new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return dateToUse.toLocaleDateString('es-ES', options);
  };

  const value: MercaditoContextType = {
    // Products
    products,
    setProducts,
    addProduct,
    updateProduct,
    deleteProduct,
    
    // Settings
    settings,
    updateSettings,
    
    // Computed values
    availableProducts,
    isEnabled,
    currentDate: getCurrentDate(),
    
    // Actions
    toggleMercadito,
    disableAllProducts,
    toggleProductAvailability,
  };

  return (
    <MercaditoContext.Provider value={value}>
      {children}
    </MercaditoContext.Provider>
  );
};

// Hook to use the context
export const useMercadito = () => {
  const context = useContext(MercaditoContext);
  if (context === undefined) {
    throw new Error('useMercadito must be used within a MercaditoProvider');
  }
  return context;
};

export default MercaditoContext;
