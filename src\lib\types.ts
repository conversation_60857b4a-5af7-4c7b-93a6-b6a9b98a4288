// Tipos para los platos del menú
export interface PlatoMenu {
  id: number;
  nombre: string;
  descripcion?: string;
  tipo: 'principal' | 'guarnicion' | 'postre';
  destacado?: boolean;
  categorias?: string[]; // etiquetas/categorías como array de string
  orden_visualizacion: number;
  fecha_creacion?: string;
  fecha_actualizacion?: string;
  sin_gluten?: boolean;
  sin_lactosa?: boolean;
  vegetariano?: boolean;
  vegano?: boolean;
  dia_semana?: number; // 1: Lunes, 2: <PERSON>es, ..., 5: Viernes
}

// Tipo para el formulario de creación/edición de platos
export interface PlatoMenuForm {
  nombre: string;
  descripcion?: string;
  tipo: 'principal' | 'guarnicion' | 'postre';
  destacado?: boolean;
  categorias?: string[];
  sin_gluten?: boolean;
  sin_lactosa?: boolean;
  vegetariano?: boolean;
  vegano?: boolean;
  dia_semana?: number; // 1: <PERSON><PERSON>, 2: <PERSON><PERSON>, ..., 5: <PERSON>iernes
}

// Tipo para los elementos arrastrables
export interface DraggableItem {
  id: number;
  orden_visualizacion: number;
}
