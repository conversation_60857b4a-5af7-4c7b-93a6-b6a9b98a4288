
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        scrolled
          ? "bg-white/90 backdrop-blur-md shadow-md py-4"
          : "bg-white py-6"
      )}
    >
      <div className="container mx-auto px-4">
        <div className="relative">
          <div className="flex w-full items-center justify-center gap-4">
            {/* Logo */}
            <div className={`${scrolled ? 'h-24' : 'h-32'} w-auto transition-all duration-300`}>
              <img
                src="/images/Cienfuegos.png"
                alt="Cien Fuegos"
                className="h-full w-auto"
              />
            </div>

            {/* Desktop Menu */}
            <div className={`hidden md:flex ${scrolled ? 'space-x-6' : 'space-x-8'} items-center transition-all duration-300`}>
              {["Inicio", "Menú", "Mercadito", "Contacto"].map((item) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase()}`}
                  className={`font-pearly ${scrolled ? 'text-2xl' : 'text-3xl'} text-gray-800 hover:text-cantina-600 transition-colors`}
                >
                  {item}
                </a>
              ))}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="md:hidden text-gray-800 absolute right-4 top-8"
            >
              {isOpen ? <X size={32} /> : <Menu size={32} />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isOpen && (
          <div className="md:hidden mt-4 py-6 bg-white/95 backdrop-blur-md rounded-lg shadow-lg animate-fade-in">
            <div className="flex flex-col space-y-6 px-4 items-center">
              {["Inicio", "Menú", "Mercadito", "Contacto"].map((item) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase()}`}
                  className="font-pearly text-3xl text-gray-800 hover:text-cantina-600 transition-colors py-3"
                  onClick={() => setIsOpen(false)}
                >
                  {item}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
