import React, { createContext, useContext, useState, ReactNode } from 'react';
import { PlatoMenu } from '@/lib/types';
import { platosMenuApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

interface DragDropContextType {
  // Platos
  platos: PlatoMenu[];
  setPlatos: React.Dispatch<React.SetStateAction<PlatoMenu[]>>;
  platosPorDia: (dia: number | null) => PlatoMenu[]; // null para platos sin día asignado
  reordenarPlatos: (items: PlatoMenu[], dia: number | null) => Promise<void>; // dia para el contexto del reordenamiento
  moverPlatoEntreDias: (platoId: number, diaOrigen: number | null, diaDestino: number | null, nuevaPosicion: number) => Promise<void>;

  // Estado de arrastre
  isDragging: boolean;
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>;

  // Cargar datos
  cargarDatos: (mostrarNotificacion?: boolean) => Promise<void>;
  diasSemana: { id: number; nombre: string }[];
}

const DragDropContext = createContext<DragDropContextType | undefined>(undefined);

const DIAS_SEMANA = [
  { id: 1, nombre: 'Lunes' },
  { id: 2, nombre: 'Martes' },
  { id: 3, nombre: 'Miércoles' },
  { id: 4, nombre: 'Jueves' },
  { id: 5, nombre: 'Viernes' },
  { id: 0, nombre: 'Sin asignar' } // Al final para facilitar el drag & drop
];

export const DragDropProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [platos, setPlatos] = useState<PlatoMenu[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const { toast } = useToast();

  // Función para cargar todos los datos
  const cargarDatos = async (mostrarNotificacion = false) => {
    try {
      const platosData = await platosMenuApi.getAll();
      setPlatos(platosData);
      if (mostrarNotificacion) {
        toast({
          title: 'Datos cargados',
          description: 'Los platos se han cargado correctamente',
        });
      }
    } catch (error) {
      console.error('Error al cargar datos:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron cargar los datos. Por favor, verifica tu conexión y permisos.',
        variant: 'destructive'
      });
    }
  };

  // Función para obtener platos por día
  const platosPorDia = (dia: number | null) => {
    // Si dia es null, filtramos por dia_semana null o undefined
    // Si dia es 0 (Sin asignar), filtramos por dia_semana null o undefined
    // Si dia es 1-5, filtramos por ese dia_semana
    return platos.filter(plato => {
      if (dia === 0 || dia === null) {
        return plato.dia_semana === null || plato.dia_semana === undefined;
      }
      return plato.dia_semana === dia;
    }).sort((a, b) => a.orden_visualizacion - b.orden_visualizacion);
  };

  // Función para reordenar platos dentro de un día específico
  const reordenarPlatos = async (items: PlatoMenu[], dia: number | null) => {
    try {
      // Actualizar el estado local: aplicamos el nuevo orden solo a los platos del día afectado
      const platosActualizados = platos.map(p => {
        const itemActualizado = items.find(i => i.id === p.id);
        // Solo actualizamos el orden si el plato pertenece al día que se está reordenando
        // y si el plato está en la lista de items reordenados.
        if ((p.dia_semana === dia || (dia === 0 && (p.dia_semana === null || p.dia_semana === undefined))) && itemActualizado) {
          const nuevoOrden = items.findIndex(i => i.id === p.id);
          return { ...p, orden_visualizacion: nuevoOrden + 1 };
        }
        return p;
      });
      setPlatos(platosActualizados);

      // Preparar los datos para la actualización en la base de datos
      // Solo enviamos los platos que fueron reordenados (los que están en 'items')
      const actualizaciones = items.map((item, index) => ({
        id: item.id,
        orden_visualizacion: index + 1,
        // Aseguramos que el dia_semana se mantenga o se asigne correctamente si es 'Sin asignar'
        dia_semana: dia === 0 ? null : dia
      }));

      if (actualizaciones.length > 0) {
        await platosMenuApi.updateOrden(actualizaciones);
      }

    } catch (error) {
      console.error('Error al reordenar platos:', error);
      toast({
        title: 'Error',
        description: 'No se pudo actualizar el orden de los platos',
        variant: 'destructive'
      });
      await cargarDatos(); // Recargar todos los datos en caso de error
    }
  };

  // Función para mover un plato entre diferentes días
  const moverPlatoEntreDias = async (platoId: number, diaOrigen: number | null, diaDestino: number | null, nuevaPosicion: number) => {
    try {
      // Encontrar el plato que se está moviendo
      const platoMovido = platos.find(p => p.id === platoId);
      if (!platoMovido) return;

      // Convertir 0 a null para "Sin asignar"
      const diaOrigenNormalizado = diaOrigen === 0 ? null : diaOrigen;
      const diaDestinoNormalizado = diaDestino === 0 ? null : diaDestino;

      // Obtener platos del día de destino
      const platosDestino = platosPorDia(diaDestinoNormalizado);

      // Actualizar el estado local
      const platosActualizados = platos.map(p => {
        if (p.id === platoId) {
          // Actualizar el plato movido
          return {
            ...p,
            dia_semana: diaDestinoNormalizado,
            orden_visualizacion: nuevaPosicion + 1
          };
        }

        // Reordenar platos en el día de destino
        if (p.dia_semana === diaDestinoNormalizado && p.id !== platoId) {
          const posicionActual = platosDestino.findIndex(pd => pd.id === p.id);
          if (posicionActual >= nuevaPosicion) {
            return { ...p, orden_visualizacion: p.orden_visualizacion + 1 };
          }
        }

        return p;
      });

      setPlatos(platosActualizados);

      // Actualizar en la base de datos
      await platosMenuApi.update(platoId, {
        dia_semana: diaDestinoNormalizado,
        orden_visualizacion: nuevaPosicion + 1
      });

      // Reordenar los platos del día de destino
      const platosDestinoActualizados = platosActualizados
        .filter(p => p.dia_semana === diaDestinoNormalizado)
        .sort((a, b) => a.orden_visualizacion - b.orden_visualizacion);

      const actualizacionesDestino = platosDestinoActualizados.map((plato, index) => ({
        id: plato.id,
        orden_visualizacion: index + 1,
        dia_semana: diaDestinoNormalizado
      }));

      if (actualizacionesDestino.length > 0) {
        await platosMenuApi.updateOrden(actualizacionesDestino);
      }

      toast({
        title: 'Plato movido',
        description: `El plato se movió correctamente${diaDestino === 0 ? ' a Sin asignar' : ` al ${DIAS_SEMANA.find(d => d.id === diaDestino)?.nombre}`}`,
      });

    } catch (error) {
      console.error('Error al mover plato entre días:', error);
      toast({
        title: 'Error',
        description: 'No se pudo mover el plato',
        variant: 'destructive'
      });
      await cargarDatos(); // Recargar todos los datos en caso de error
    }
  };

  const value = {
    platos,
    setPlatos,
    platosPorDia,
    reordenarPlatos,
    moverPlatoEntreDias,
    isDragging,
    setIsDragging,
    cargarDatos,
    diasSemana: DIAS_SEMANA
  };

  return (
    <DragDropContext.Provider value={value}>
      {children}
    </DragDropContext.Provider>
  );
};

export const useDragDrop = () => {
  const context = useContext(DragDropContext);
  if (context === undefined) {
    throw new Error('useDragDrop debe ser usado dentro de un DragDropProvider');
  }
  return context;
};
