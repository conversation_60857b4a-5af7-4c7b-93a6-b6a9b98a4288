import React, { createContext, useContext, useState, ReactNode } from 'react';
import { PlatoMenu } from '@/lib/types';
import { platosMenuApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

interface DragDropContextType {
  // Platos
  platos: PlatoMenu[];
  setPlatos: React.Dispatch<React.SetStateAction<PlatoMenu[]>>;
  platosPorDia: (dia: number | null) => PlatoMenu[]; // null para platos sin día asignado
  reordenarPlatos: (items: PlatoMenu[], dia: number | null) => Promise<void>; // dia para el contexto del reordenamiento

  // Estado de arrastre
  isDragging: boolean;
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>;

  // Cargar datos
  cargarDatos: (mostrarNotificacion?: boolean) => Promise<void>;
  diasSemana: { id: number; nombre: string }[];
}

const DragDropContext = createContext<DragDropContextType | undefined>(undefined);

const DIAS_SEMANA = [
  { id: 1, nombre: 'Lunes' },
  { id: 2, nombre: 'Martes' },
  { id: 3, nombre: 'Miércoles' },
  { id: 4, nombre: 'Jueves' },
  { id: 5, nombre: 'Viernes' },
  { id: 0, nombre: 'Sin asignar' } // Usaremos 0 o null para platos sin día
];

export const DragDropProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [platos, setPlatos] = useState<PlatoMenu[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const { toast } = useToast();

  // Función para cargar todos los datos
  const cargarDatos = async (mostrarNotificacion = false) => {
    try {
      const platosData = await platosMenuApi.getAll();
      setPlatos(platosData);
      if (mostrarNotificacion) {
        toast({
          title: 'Datos cargados',
          description: 'Los platos se han cargado correctamente',
        });
      }
    } catch (error) {
      console.error('Error al cargar datos:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron cargar los datos. Por favor, verifica tu conexión y permisos.',
        variant: 'destructive'
      });
    }
  };

  // Función para obtener platos por día
  const platosPorDia = (dia: number | null) => {
    // Si dia es null, filtramos por dia_semana null o undefined
    // Si dia es 0 (Sin asignar), filtramos por dia_semana null o undefined
    // Si dia es 1-5, filtramos por ese dia_semana
    return platos.filter(plato => {
      if (dia === 0 || dia === null) {
        return plato.dia_semana === null || plato.dia_semana === undefined;
      }
      return plato.dia_semana === dia;
    }).sort((a, b) => a.orden_visualizacion - b.orden_visualizacion);
  };

  // Función para reordenar platos dentro de un día específico
  const reordenarPlatos = async (items: PlatoMenu[], dia: number | null) => {
    try {
      // Actualizar el estado local: aplicamos el nuevo orden solo a los platos del día afectado
      const platosActualizados = platos.map(p => {
        const itemActualizado = items.find(i => i.id === p.id);
        // Solo actualizamos el orden si el plato pertenece al día que se está reordenando
        // y si el plato está en la lista de items reordenados.
        if ((p.dia_semana === dia || (dia === 0 && (p.dia_semana === null || p.dia_semana === undefined))) && itemActualizado) {
          const nuevoOrden = items.findIndex(i => i.id === p.id);
          return { ...p, orden_visualizacion: nuevoOrden + 1 };
        }
        return p;
      });
      setPlatos(platosActualizados);

      // Preparar los datos para la actualización en la base de datos
      // Solo enviamos los platos que fueron reordenados (los que están en 'items')
      const actualizaciones = items.map((item, index) => ({
        id: item.id,
        orden_visualizacion: index + 1,
        // Aseguramos que el dia_semana se mantenga o se asigne correctamente si es 'Sin asignar'
        dia_semana: dia === 0 ? null : dia 
      }));

      if (actualizaciones.length > 0) {
        await platosMenuApi.updateOrden(actualizaciones);
      }

    } catch (error) {
      console.error('Error al reordenar platos:', error);
      toast({
        title: 'Error',
        description: 'No se pudo actualizar el orden de los platos',
        variant: 'destructive'
      });
      await cargarDatos(); // Recargar todos los datos en caso de error
    }
  };

  const value = {
    platos,
    setPlatos,
    platosPorDia,
    reordenarPlatos,
    isDragging,
    setIsDragging,
    cargarDatos,
    diasSemana: DIAS_SEMANA
  };

  return (
    <DragDropContext.Provider value={value}>
      {children}
    </DragDropContext.Provider>
  );
};

export const useDragDrop = () => {
  const context = useContext(DragDropContext);
  if (context === undefined) {
    throw new Error('useDragDrop debe ser usado dentro de un DragDropProvider');
  }
  return context;
};
