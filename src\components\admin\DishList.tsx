import React, { useState, useEffect } from 'react';
import { motion, Reorder, useDragControls } from 'framer-motion';
import { PlatoMenu } from '@/lib/types';
import { useDragDrop } from './DragDropContext';
import { platosMenuApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GripVertical, Edit, Trash2, Plus, Star, StarOff } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DishForm } from './DishForm';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface DishItemProps {
  plato: PlatoMenu;
  onEdit: (plato: PlatoMenu) => void;
  onDelete: (id: number) => void;
  onToggleDestacado: (id: number, destacado: boolean) => void;
}

const DishItem: React.FC<DishItemProps> = ({ plato, onEdit, onDelete, onToggleDestacado }) => {
  const controls = useDragControls();
  const { setIsDragging } = useDragDrop();

  // Función para obtener el color del badge según el tipo de plato
  const getBadgeVariant = (tipo: string) => {
    switch (tipo) {
      case 'principal':
        return 'default';
      case 'guarnicion':
        return 'secondary';
      case 'postre':
        return 'outline';
      default:
        return 'default';
    }
  };

  return (
    <Reorder.Item
      value={plato}
      id={plato.id.toString()}
      dragControls={controls}
      dragListener={false}
      onDragStart={() => setIsDragging(true)}
      onDragEnd={() => setIsDragging(false)}
    >
      <Card className="mb-2 cursor-move">
        <CardContent className="p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div
              className="cursor-grab active:cursor-grabbing"
              onPointerDown={(e) => controls.start(e)}
            >
              <GripVertical className="h-5 w-5 text-gray-400" />
            </div>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="font-medium">{plato.nombre}</span>
                {plato.destacado && (
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                )}
                <Badge variant={getBadgeVariant(plato.tipo) as any}>
                  {plato.tipo}
                </Badge>
              </div>
              {plato.descripcion && (
                <span className="text-sm text-gray-500 line-clamp-1">
                  {plato.descripcion}
                </span>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onToggleDestacado(plato.id, !plato.destacado)}
              title={plato.destacado ? "Quitar destacado" : "Marcar como destacado"}
            >
              {plato.destacado ? (
                <StarOff className="h-4 w-4" />
              ) : (
                <Star className="h-4 w-4" />
              )}
            </Button>
            <Button variant="ghost" size="icon" onClick={() => onEdit(plato)}>
              <Edit className="h-4 w-4" />
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Trash2 className="h-4 w-4 text-red-500" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Esta acción eliminará el plato "{plato.nombre}" y no se puede deshacer.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction onClick={() => onDelete(plato.id)}>
                    Eliminar
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>
    </Reorder.Item>
  );
};

export const DishList: React.FC = () => {
  const { platos, reordenarPlatos, cargarDatos } = useDragDrop();
  const [selectedTag, setSelectedTag] = useState<string>('todos');
  const [filteredPlatos, setFilteredPlatos] = useState<PlatoMenu[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingDish, setEditingDish] = useState<PlatoMenu | null>(null);
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const { toast } = useToast();

  // Get unique tags from all dishes
  useEffect(() => {
    const uniqueTags = new Set<string>();
    platos.forEach(plato => {
      plato.categorias?.forEach(cat => uniqueTags.add(cat));
    });
    setAvailableTags(['todos', ...Array.from(uniqueTags).sort()]);
  }, [platos]);

  // Update filtered dishes when selected tag changes
  useEffect(() => {
    if (selectedTag === 'todos') {
      setFilteredPlatos(platos.sort((a, b) => a.orden_visualizacion - b.orden_visualizacion));
    } else {
      setFilteredPlatos(platos.filter(plato => plato.categorias?.includes(selectedTag)));
    }
  }, [selectedTag, platos]);

  const handleTagChange = (value: string) => {
    setSelectedTag(value);
  };

  const handleEdit = (plato: PlatoMenu) => {
    setEditingDish(plato);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await platosMenuApi.delete(id);
      toast({
        title: 'Plato eliminado',
        description: 'El plato ha sido eliminado correctamente',
      });
      await cargarDatos(true);
    } catch (error) {
      console.error('Error al eliminar plato:', error);
      toast({
        title: 'Error',
        description: 'No se pudo eliminar el plato',
        variant: 'destructive',
      });
    }
  };

  const handleToggleDestacado = async (id: number, destacado: boolean) => {
    try {
      const plato = platos.find(p => p.id === id);
      if (plato) {
        await platosMenuApi.update(id, { ...plato, destacado });
        await cargarDatos(true);
      }
    } catch (error) {
      console.error('Error al actualizar plato:', error);
      toast({
        title: 'Error',
        description: 'No se pudo actualizar el plato',
        variant: 'destructive',
      });
    }
  };

  const handleReorder = async (newOrder: PlatoMenu[]) => {
    // Update order numbers and save
    const updatedOrder = newOrder.map((plato, index) => ({
      ...plato,
      orden_visualizacion: index + 1
    }));

    // Update the order in all filtered dishes
    await reordenarPlatos(updatedOrder, null);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingDish(null);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Platos</CardTitle>
        <div className="flex gap-2">
          <Select
            value={selectedTag}
            onValueChange={handleTagChange}
            name="filtro-etiquetas"
          >
            <SelectTrigger id="filtro-etiquetas" className="w-[180px]">
              <SelectValue placeholder="Filtrar por..." />
            </SelectTrigger>
            <SelectContent>
              {availableTags.map((tag) => (
                <SelectItem key={tag} value={tag}>
                  {tag === 'todos' ? '📋 Todos los platos' : `🏷️ ${tag}`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Plato
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingDish ? 'Editar Plato' : 'Nuevo Plato'}
                </DialogTitle>
              </DialogHeader>
              <DishForm
                plato={editingDish}
                onSuccess={handleCloseDialog}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {filteredPlatos.length > 0 ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {selectedTag === 'todos'
                  ? `Mostrando ${filteredPlatos.length} platos`
                  : `${filteredPlatos.length} platos con etiqueta "${selectedTag}"`
                }
              </p>
            </div>
            <Reorder.Group
              axis="y"
              values={filteredPlatos}
              onReorder={setFilteredPlatos}
              layoutScroll
              className="space-y-2"
              onLayoutChange={() => handleReorder(filteredPlatos)}
            >
              {filteredPlatos.map((plato) => (
                <DishItem
                  key={plato.id}
                  plato={plato}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onToggleDestacado={handleToggleDestacado}
                />
              ))}
            </Reorder.Group>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {selectedTag === 'todos' ? (
              <div>
                <p className="mb-2">No hay platos creados aún.</p>
                <p className="text-sm">¡Crea tu primer plato para comenzar!</p>
              </div>
            ) : (
              <div>
                <p className="mb-2">No hay platos con la etiqueta "{selectedTag}".</p>
                <p className="text-sm">Crea un plato nuevo o cambia el filtro.</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
