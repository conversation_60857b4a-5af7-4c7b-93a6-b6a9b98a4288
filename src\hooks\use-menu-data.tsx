import { useState, useEffect } from 'react';
import { PlatoMenu } from '@/lib/types';
import { supabase } from '@/lib/supabase';

export interface DiaSemana {
  id: number; // 1: Lunes, 2: <PERSON><PERSON>, ..., 5: Viernes
  nombre: string;
}

export interface MenuData {
  dias: DiaSemana[];
  platos: PlatoMenu[];
  platosPorDia: (diaId: number) => PlatoMenu[];
  isLoading: boolean;
  error: string | null;
}

export function useMenuData(): MenuData {
  const [dias, setDias] = useState<DiaSemana[]>([
    { id: 1, nombre: 'Lunes' },
    { id: 2, nombre: 'Martes' },
    { id: 3, nombre: 'Miércoles' },
    { id: 4, nombre: 'Jueves' },
    { id: 5, nombre: 'Viernes' },
  ]);
  const [platos, setPlatos] = useState<PlatoMenu[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true);
        setError(null);

        // Obtener platos
        // Asumimos que la tabla 'platos_menu' tiene una columna 'dia_semana' (número 1-5)
        // o una columna que pueda usarse para filtrar por día.
        // Si un plato puede estar en múltiples días, la estructura de datos y la consulta necesitarán ajustarse.
        const { data: platosData, error: platosError } = await supabase
          .from('platos_menu')
          .select('*')
          .order('orden_visualizacion', { ascending: true });

        if (platosError) throw new Error(platosError.message);

        setPlatos(platosData || []);
      } catch (err) {
        console.error('Error al cargar datos del menú:', err);
        setError(err instanceof Error ? err.message : 'Error desconocido al cargar el menú');
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, []);

  // Función para obtener platos por día de la semana
  // Asume que cada plato tiene una propiedad 'dia_semana' (número 1-5)
  // o una lógica para determinar a qué día pertenece.
  // Si un plato puede pertenecer a varios días, esta lógica necesitará ser más compleja
  // (ej. 'dia_semana' podría ser un array de números).
  const platosPorDia = (diaId: number): PlatoMenu[] => {
    return platos
      .filter(plato => {
        // Ejemplo: si 'plato.dia_semana' es un número que representa el día
        // Si la estructura es diferente (ej. un array de días en el plato), ajustar aquí.
        // Por ahora, asumimos que 'plato.dia_semana' es un número o una cadena que coincide con diaId
        // o que hay una tabla de relación plato-día.
        // Para este ejemplo, asumiremos que 'plato.dia_semana' es el ID del día.
        return plato.dia_semana === diaId;
      })
      .sort((a, b) => a.orden_visualizacion - b.orden_visualizacion);
  };

  return {
    dias,
    platos,
    platosPorDia,
    isLoading,
    error
  };
}
