import React from 'react';
import { Button } from '@/components/ui/button';
import { Calendar, List, Plus, Menu } from 'lucide-react';

interface MobileBottomNavProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onNewDish: () => void;
  onMenuOpen: () => void;
}

export const MobileBottomNav: React.FC<MobileBottomNavProps> = ({
  activeTab,
  onTabChange,
  onNewDish,
  onMenuOpen
}) => {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 z-50 md:hidden">
      <div className="grid grid-cols-4 h-16">
        {/* Menú Semanal */}
        <Button
          variant={activeTab === 'menu' ? 'default' : 'ghost'}
          onClick={() => onTabChange('menu')}
          className="h-full rounded-none flex flex-col gap-1 text-xs"
        >
          <Calendar className="h-5 w-5" />
          <span>Menú</span>
        </Button>

        {/* Todos los Platos */}
        <Button
          variant={activeTab === 'platos' ? 'default' : 'ghost'}
          onClick={() => onTabChange('platos')}
          className="h-full rounded-none flex flex-col gap-1 text-xs"
        >
          <List className="h-5 w-5" />
          <span>Platos</span>
        </Button>

        {/* Nuevo Plato */}
        <Button
          variant="ghost"
          onClick={onNewDish}
          className="h-full rounded-none flex flex-col gap-1 text-xs text-blue-600 dark:text-blue-400"
        >
          <Plus className="h-5 w-5" />
          <span>Nuevo</span>
        </Button>

        {/* Menú */}
        <Button
          variant="ghost"
          onClick={onMenuOpen}
          className="h-full rounded-none flex flex-col gap-1 text-xs"
        >
          <Menu className="h-5 w-5" />
          <span>Más</span>
        </Button>
      </div>
    </div>
  );
};
