import { useState, useEffect, useCallback } from 'react';
import {
  mercaditoApi,
  mercaditoSettingsApi,
  MercaditoItem,
  MercaditoSettings,
  getFormattedDate
} from '@/lib/mercadito-api';
import { useToast } from '@/hooks/use-toast';

export const useMercadito = () => {
  const [items, setItems] = useState<MercaditoItem[]>([]);
  const [settings, setSettings] = useState<MercaditoSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Load data from database
  const loadData = useCallback(async (showToast = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const [itemsData, settingsData] = await Promise.all([
        mercaditoApi.getAll(),
        mercaditoSettingsApi.get()
      ]);

      setItems(itemsData);
      setSettings(settingsData);

      if (showToast) {
        toast({
          title: 'Datos actualizados',
          description: 'Los datos del mercadito se han actualizado correctamente',
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error loading mercadito data:', err);

      if (showToast) {
        toast({
          title: 'Error',
          description: 'No se pudieron cargar los datos del mercadito',
          variant: 'destructive',
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Item management functions
  const createItem = async (itemData: Omit<MercaditoItem, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newItem = await mercaditoApi.create(itemData);
      setItems(prev => [...prev, newItem]);
      toast({
        title: 'Producto creado',
        description: `${newItem.nombre} ha sido creado correctamente`,
      });
      return newItem;
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudo crear el producto',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const updateItem = async (id: number, updates: Partial<MercaditoItem>) => {
    try {
      const updatedItem = await mercaditoApi.update(id, updates);
      setItems(prev => prev.map(item => item.id === id ? updatedItem : item));
      toast({
        title: 'Producto actualizado',
        description: `${updatedItem.nombre} ha sido actualizado correctamente`,
      });
      return updatedItem;
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudo actualizar el producto',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const deleteItem = async (id: number) => {
    try {
      const item = items.find(i => i.id === id);
      await mercaditoApi.delete(id);
      setItems(prev => prev.filter(item => item.id !== id));
      toast({
        title: 'Producto eliminado',
        description: `${item?.nombre || 'El producto'} ha sido eliminado correctamente`,
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudo eliminar el producto',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const toggleItemAvailability = async (id: number, disponible: boolean) => {
    try {
      const updatedItem = await mercaditoApi.toggleAvailability(id, disponible);
      setItems(prev => prev.map(item => item.id === id ? updatedItem : item));
      toast({
        title: disponible ? 'Producto disponible' : 'Producto no disponible',
        description: `${updatedItem.nombre} ${disponible ? 'está ahora disponible' : 'ya no está disponible'}`,
      });
      return updatedItem;
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudo actualizar la disponibilidad del producto',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const disableAllItems = async () => {
    try {
      await mercaditoApi.disableAll();
      setItems(prev => prev.map(item => ({ ...item, disponible: false })));
      toast({
        title: 'Todos los productos deshabilitados',
        description: 'Ningún producto aparecerá como disponible',
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudieron deshabilitar todos los productos',
        variant: 'destructive',
      });
      throw err;
    }
  };

  // Settings management functions
  const updateSettings = async (updates: Partial<Omit<MercaditoSettings, 'id' | 'updated_at'>>) => {
    try {
      const updatedSettings = await mercaditoSettingsApi.update(updates);
      setSettings(updatedSettings);
      return updatedSettings;
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudieron actualizar las configuraciones',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const toggleMercadito = async () => {
    try {
      const updatedSettings = await mercaditoSettingsApi.toggleEnabled();
      setSettings(updatedSettings);
      toast({
        title: updatedSettings.enabled ? 'Mercadito habilitado' : 'Mercadito deshabilitado',
        description: updatedSettings.enabled
          ? 'El mercadito aparecerá en el frontend'
          : 'El mercadito no aparecerá en el frontend',
      });
      return updatedSettings;
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudo cambiar el estado del mercadito',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const setCustomDate = async (date: string | null) => {
    try {
      const updatedSettings = await mercaditoSettingsApi.setCustomDate(date);
      setSettings(updatedSettings);
      toast({
        title: 'Fecha actualizada',
        description: date
          ? `Fecha personalizada establecida: ${getFormattedDate(date)}`
          : 'Fecha restablecida a hoy',
      });
      return updatedSettings;
    } catch (err) {
      toast({
        title: 'Error',
        description: 'No se pudo actualizar la fecha',
        variant: 'destructive',
      });
      throw err;
    }
  };

  // Computed values
  const availableItems = items.filter(item => item.disponible && (settings?.enabled ?? true));
  const isEnabled = settings?.enabled ?? true;
  const currentDate = getFormattedDate(settings?.custom_date);

  return {
    // Data
    items,
    settings,
    isLoading,
    error,

    // Computed values
    availableItems,
    isEnabled,
    currentDate,

    // Item management
    createItem,
    updateItem,
    deleteItem,
    toggleItemAvailability,
    disableAllItems,

    // Settings management
    updateSettings,
    toggleMercadito,
    setCustomDate,

    // Utility
    loadData,
  };
};
