
import { cn } from "@/lib/utils";
import { ArrowDown, Clock, Coffee, Utensils, Phone } from "lucide-react";

const HeroSection = () => {
  return (
    <section
      id="inicio"
      className="relative min-h-screen flex items-center overflow-hidden pt-56 pb-8 bg-white"
    >
      <div className="container mx-auto px-4 relative z-20 mt-8">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Lado derecho - Imágenes con estilo asimétrico y animación (ahora a la izquierda en desktop) */}
          <div className="relative md:order-1 min-h-[500px]">
            <div className="relative w-full h-[500px]">
              {/* Imagen superior izquierda - similar a la foto de referencia */}
              <div className="absolute top-0 left-0 w-[60%] h-[220px] animate-float animation-delay-200" style={{ transform: 'rotate(-2deg)' }}>
                <div className="rounded-3xl overflow-hidden shadow-xl h-full">
                  <img
                    src="/images/desayuno-completo.jpeg"
                    alt="Comida de la cantina"
                    className="w-full h-full object-cover"
                    loading="eager"
                  />
                </div>
              </div>

              {/* Imagen superior derecha - similar a la foto de referencia */}
              <div className="absolute top-0 right-0 w-[45%] h-[250px] animate-float animation-delay-300" style={{ transform: 'rotate(2deg)' }}>
                <div className="rounded-3xl overflow-hidden shadow-xl h-full">
                  <img
                    src="/images/desayuno-ip.jpeg"
                    alt="Servicio de café"
                    className="w-full h-full object-cover"
                    loading="eager"
                  />
                </div>
              </div>

              {/* Imagen inferior izquierda - similar a la foto de referencia */}
              <div className="absolute bottom-0 left-0 w-[55%] h-[220px] animate-float animation-delay-100" style={{ transform: 'rotate(1deg)' }}>
                <div className="rounded-3xl overflow-hidden shadow-xl h-full">
                  <img
                    src="/images/mesa-completa.webp"
                    alt="Servicio de catering"
                    className="w-full h-full object-cover"
                    loading="eager"
                  />
                </div>
              </div>

              {/* Imagen inferior derecha - similar a la foto de referencia */}
              <div className="absolute bottom-0 right-0 w-[50%] h-[180px] animate-float animation-delay-400" style={{ transform: 'rotate(-1deg)' }}>
                <div className="rounded-3xl overflow-hidden shadow-xl h-full">
                  <img
                    src="/images/mesa-vino.webp"
                    alt="Comida de la cantina"
                    className="w-full h-full object-cover"
                    loading="eager"
                  />
                </div>
              </div>
            </div>


          </div>

          {/* Lado izquierdo - Texto (ahora a la derecha en desktop) */}
          <div className="space-y-8 md:order-2">
            <h1 className="font-pearly text-6xl md:text-7xl text-black mb-6 animate-fade-in animation-delay-100 font-bold">
              <span className="block">Cantina del</span>
              <span className="text-cantina-600">IP Montevideo</span>
            </h1>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 animate-fade-in animation-delay-300">
              <div className="bg-cantina-50 rounded-xl p-5 shadow-md transform hover:scale-105 transition-transform border border-cantina-100">
                <div className="flex items-start">
                  <Clock className="text-cantina-600 bg-cantina-100 p-2 rounded-lg mr-3 flex-shrink-0" size={30} />
                  <div>
                    <h3 className="text-2xl text-cantina-800 mb-2 font-pearly">Horario</h3>
                    <p className="text-gray-800 text-lg mb-1" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>9:00 a 17:00</p>
                    <p className="text-gray-800 text-lg" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>Almuerzo 12:00 a 14:30</p>
                  </div>
                </div>
              </div>

              <div className="bg-cantina-50 rounded-xl p-5 shadow-md transform hover:scale-105 transition-transform border border-cantina-100">
                <div className="flex items-start">
                  <Phone className="text-cantina-600 bg-cantina-100 p-2 rounded-lg mr-3 flex-shrink-0" size={30} />
                  <div>
                    <h3 className="text-2xl text-cantina-800 mb-2 font-pearly">Contacto</h3>
                    <p className="text-gray-800 text-lg" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>098362622</p>
                  </div>
                </div>
              </div>

              <div className="bg-cantina-50 rounded-xl p-5 shadow-md transform hover:scale-105 transition-transform border border-cantina-100">
                <div className="flex items-start">
                  <Utensils className="text-cantina-600 bg-cantina-100 p-2 rounded-lg mr-3 flex-shrink-0" size={30} />
                  <div>
                    <h3 className="text-2xl text-cantina-800 mb-2 font-pearly">Servicios</h3>
                    <p className="text-gray-800 text-lg" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>Servicios de almuerzo</p>
                  </div>
                </div>
              </div>

              <div className="bg-cantina-50 rounded-xl p-5 shadow-md transform hover:scale-105 transition-transform border border-cantina-100">
                <div className="flex items-start">
                  <Coffee className="text-cantina-600 bg-cantina-100 p-2 rounded-lg mr-3 flex-shrink-0" size={30} />
                  <div>
                    <h3 className="text-2xl text-cantina-800 mb-2 font-pearly">Adicionales</h3>
                    <p className="text-gray-800 text-lg mb-1" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>Servicios de café</p>
                    <p className="text-gray-800 text-lg" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>Servicios de catering</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-10 animate-fade-in animation-delay-400">
              <a
                href="#menú"
                className="bg-cantina-600 text-white hover:bg-cantina-700 px-10 py-4 rounded-lg font-pearly text-2xl transition-all hover:shadow-lg inline-flex items-center hover:scale-105"
              >
                Ver Menú
                <ArrowDown className="ml-3 h-5 w-5 rotate-90" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
