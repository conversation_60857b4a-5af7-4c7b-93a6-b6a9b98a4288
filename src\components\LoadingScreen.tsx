import { useEffect, useState } from "react";
import <PERSON><PERSON> from "lottie-react";
import { useRef } from "react";

interface LoadingScreenProps {
  children?: React.ReactNode;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [fireAnimation, setFireAnimation] = useState<any>(null);
  const lottieRef = useRef(null);

  useEffect(() => {
    // Cargar el archivo JSON de animación
    fetch('/images/fire.json')
      .then(response => response.json())
      .then(data => setFireAnimation(data))
      .catch(error => console.error('Error loading animation:', error));

    // Simulate loading time (you can replace this with actual loading logic)
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (!isLoading) {
    return <>{children}</>;
  }

  return (
    <div className="fixed inset-0 bg-cantina-700 flex items-center justify-center z-[100]">
      <div className="relative">
        {/* Fuego en el fondo */}
        <div className="absolute inset-0 -z-10 scale-125">
          {fireAnimation && (
            <Lottie
              animationData={fireAnimation}
              loop={true}
              className="w-full h-full"
              lottieRef={lottieRef}
            />
          )}
        </div>

        {/* Serpiente como elemento principal */}
        <div className="flex flex-col items-center">
          <div className="w-96 h-96 animate-float">
            <img
              src="/images/serpiente-chef.svg"
              alt="Chef Snake"
              className="w-full h-full object-contain"
            />
          </div>
          <h1 className="text-white text-5xl font-pearly mt-8">Cien Fuegos</h1>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
