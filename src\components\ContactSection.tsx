
import { MapPin, Clock, Phone, MessageCircle } from "lucide-react";

const ContactSection = () => {
  return (
    <section id="contacto" className="py-20 bg-white relative overflow-hidden">
      {/* Decorative elements with animation */}
      <div className="absolute bottom-20 -left-24 w-48 h-48 rounded-full bg-cantina-100 opacity-40 animate-float animation-delay-200"></div>
      <div className="absolute top-32 -right-12 w-32 h-32 rounded-full bg-cantina-200 opacity-30 animate-float animation-delay-300"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl font-pearly text-gray-900 mb-4">Visítanos</h2>
          <div className="w-24 h-1 bg-cantina-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Estamos ubicados en una zona céntrica y accesible.
            ¡Te esperamos para brindarte una experiencia culinaria única!
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12">
          <div className="bg-gray-100 rounded-2xl overflow-hidden h-80 md:h-auto shadow-xl transform hover:rotate-1 transition-transform duration-500 animate-float animation-delay-200">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d973.0481689484634!2d-56.118821514741974!3d-34.88308224000685!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x959f812a12a5ff4f%3A0xd495cc83077d3835!2sInstitut%20Pasteur!5e0!3m2!1ses!2suy!4v1748285520793!5m2!1ses!2suy"
              width="100%"
              height="100%"
              style={{border: 0}}
              allowFullScreen={true}
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="Ubicación de Cien Fuegos Cantina en Instituto Pasteur"
            />
          </div>

          <div className="space-y-8 animate-fade-in animation-delay-100">
            <div>
              <h3 className="text-2xl font-pearly text-gray-800 mb-4">Información de Contacto</h3>

              <div className="space-y-6">
                <div className="flex items-start transform hover:scale-105 transition-transform">
                  <div className="bg-cantina-50 p-3 rounded-full mr-4">
                    <MapPin className="text-cantina-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-pearly text-lg text-gray-800">Dirección</h4>
                    <p className="text-gray-600">
                      Instituto Pasteur<br />
                      Mataojo 2020<br />
                      Montevideo, Uruguay
                    </p>
                    <p className="text-sm text-cantina-600 mt-1 font-medium">
                      *La cantina se encuentra dentro del Instituto Pasteur
                    </p>
                  </div>
                </div>

                <div className="flex items-start transform hover:scale-105 transition-transform">
                  <div className="bg-cantina-50 p-3 rounded-full mr-4">
                    <Clock className="text-cantina-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-pearly text-lg text-gray-800">Horarios</h4>
                    <p className="text-gray-600">
                      Lunes a Viernes:  09:00  PM - 18:00 PM<br />
                      Viernes y Sábado: Cerrado<br />
                      Domingo: Cerrado</p>
                  </div>
                </div>

                <div className="flex items-start transform hover:scale-105 transition-transform">
                  <div className="bg-cantina-50 p-3 rounded-full mr-4">
                    <Phone className="text-cantina-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-pearly text-lg text-gray-800">Teléfono</h4>
                    <p className="text-gray-600">+598 098362622</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-cantina-50 p-6 rounded-2xl border border-cantina-100 shadow-md transform hover:scale-102 transition-transform animate-float animation-delay-300">
              <h4 className="text-xl font-pearly text-cantina-800 mb-3">Reservaciones</h4>
              <p className="text-gray-700 mb-4">
                Recomendamos hacer reservaciones con anticipación,
                especialmente para los fines de semana.
              </p>
              <a
                href="https://wa.me/598098362622"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-cantina-600 hover:bg-cantina-700 text-white font-pearly px-6 py-3 rounded-lg transition-all hover:shadow-lg hover:scale-105"
              >
                <MessageCircle className="mr-2" size={20} />
                Escríbenos para Reservar
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
