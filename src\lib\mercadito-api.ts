import { supabase, supabaseAdmin } from './supabase';

// Types
export interface MercaditoItem {
  id: number;
  nombre: string;
  descripcion?: string;
  precio?: number;
  categoria?: string;
  disponible: boolean;
  imagen_url?: string;
  orden_visualizacion: number;
  created_at: string;
  updated_at: string;
}

export interface MercaditoSettings {
  id: number;
  enabled: boolean;
  custom_date?: string;
  updated_at: string;
}

// API functions for mercadito items
export const mercaditoApi = {
  // Get all items
  async getAll(): Promise<MercaditoItem[]> {
    const { data, error } = await supabase
      .from('mercadito_items')
      .select('*')
      .order('orden_visualizacion', { ascending: true });

    if (error) {
      console.error('Error fetching mercadito items:', error);
      throw error;
    }

    return data || [];
  },

  // Get available items only
  async getAvailable(): Promise<MercaditoItem[]> {
    const { data, error } = await supabase
      .from('mercadito_items')
      .select('*')
      .eq('disponible', true)
      .order('orden_visualizacion', { ascending: true });

    if (error) {
      console.error('Error fetching available mercadito items:', error);
      throw error;
    }

    return data || [];
  },

  // Create new item
  async create(item: Omit<MercaditoItem, 'id' | 'created_at' | 'updated_at'>): Promise<MercaditoItem> {
    const { data, error } = await supabaseAdmin
      .from('mercadito_items')
      .insert([{
        ...item,
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating mercadito item:', error);
      throw error;
    }

    return data;
  },

  // Update item
  async update(id: number, updates: Partial<MercaditoItem>): Promise<MercaditoItem> {
    const { data, error } = await supabaseAdmin
      .from('mercadito_items')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating mercadito item:', error);
      throw error;
    }

    return data;
  },

  // Delete item
  async delete(id: number): Promise<void> {
    const { error } = await supabaseAdmin
      .from('mercadito_items')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting mercadito item:', error);
      throw error;
    }
  },

  // Toggle availability
  async toggleAvailability(id: number, disponible: boolean): Promise<MercaditoItem> {
    return this.update(id, { disponible });
  },

  // Disable all items
  async disableAll(): Promise<void> {
    const { error } = await supabaseAdmin
      .from('mercadito_items')
      .update({
        disponible: false,
        updated_at: new Date().toISOString()
      })
      .neq('id', 0); // Update all records

    if (error) {
      console.error('Error disabling all mercadito items:', error);
      throw error;
    }
  },

  // Reorder items
  async reorder(items: { id: number; orden_visualizacion: number }[]): Promise<void> {
    const updates = items.map(item =>
      supabaseAdmin
        .from('mercadito_items')
        .update({
          orden_visualizacion: item.orden_visualizacion,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.id)
    );

    const results = await Promise.all(updates);

    for (const result of results) {
      if (result.error) {
        console.error('Error reordering mercadito items:', result.error);
        throw result.error;
      }
    }
  }
};

// API functions for mercadito settings
export const mercaditoSettingsApi = {
  // Get settings
  async get(): Promise<MercaditoSettings> {
    const { data, error } = await supabase
      .from('mercadito_settings')
      .select('*')
      .limit(1)
      .single();

    if (error) {
      console.error('Error fetching mercadito settings:', error);
      throw error;
    }

    return data;
  },

  // Update settings
  async update(updates: Partial<Omit<MercaditoSettings, 'id' | 'updated_at'>>): Promise<MercaditoSettings> {
    // First, get the current settings to know the ID
    const currentSettings = await this.get();

    const { data, error } = await supabaseAdmin
      .from('mercadito_settings')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', currentSettings.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating mercadito settings:', error);
      throw error;
    }

    return data;
  },

  // Toggle mercadito enabled/disabled
  async toggleEnabled(): Promise<MercaditoSettings> {
    const currentSettings = await this.get();
    return this.update({ enabled: !currentSettings.enabled });
  },

  // Set custom date
  async setCustomDate(date: string | null): Promise<MercaditoSettings> {
    console.log('API: Estableciendo fecha personalizada:', date);
    const result = await this.update({ custom_date: date });
    console.log('API: Resultado de actualización:', result);
    return result;
  }
};

// Helper function to get formatted date
export const getFormattedDate = (customDate?: string | null): string => {
  let dateToUse: Date;

  if (customDate) {
    // Para evitar problemas de zona horaria, agregamos 'T12:00:00' para que sea mediodía UTC
    const dateString = customDate.includes('T') ? customDate : `${customDate}T12:00:00`;
    dateToUse = new Date(dateString);
  } else {
    dateToUse = new Date();
  }

  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };
  return dateToUse.toLocaleDateString('es-ES', options);
};
