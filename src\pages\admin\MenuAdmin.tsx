import React, { useState, useEffect } from 'react';
import { MenuAdminLayout } from '@/components/admin/MenuAdminLayout';
import { Toaster } from '@/components/ui/toaster';
import AdminAuth from '@/components/admin/AdminAuth';

const MenuAdmin: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Verify admin auth
  useEffect(() => {
    const initAuth = async () => {
      try {
        const authStatus = localStorage.getItem('admin_authenticated');
        if (authStatus === 'true') {
          // Initialize Supabase
          const { initializeSupabase } = await import('@/lib/supabase');
          await initializeSupabase();
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem('admin_authenticated');
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };
    initAuth();
  }, []);

  const handleAuthenticated = () => {
    // Guardar el estado de autenticación en localStorage
    localStorage.setItem('admin_authenticated', 'true');
    setIsAuthenticated(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster />
      {isLoading ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : isAuthenticated ? (
        <MenuAdminLayout />
      ) : (
        <AdminAuth onAuthenticated={handleAuthenticated} />
      )}
    </div>
  );
};

export default MenuAdmin;
