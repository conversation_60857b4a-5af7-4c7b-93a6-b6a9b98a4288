
import { Utensils, Clock, Users } from "lucide-react";

const AboutSection = () => {
  return (
    <section id="nosotros" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl font-pearly text-gray-900 mb-4">Nuestra Historia</h2>
          <div className="w-24 h-1 bg-cantina-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Desde 1985, <PERSON>ien Fuegos ha sido el lugar donde la tradición culinaria
            se encuentra con la innovación.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div className="relative min-h-[500px]">
            <div className="relative w-full h-[500px]">
              {/* Imagen superior - similar a la foto de referencia */}
              <div className="absolute top-0 left-[10%] w-[80%] h-[300px] animate-float animation-delay-200" style={{ transform: 'rotate(2deg)' }}>
                <div className="rounded-3xl overflow-hidden shadow-xl h-full">
                  <img
                    src="/images/mesa-completa.webp"
                    alt="Nuestra historia"
                    className="w-full h-full object-cover"
                    loading="eager"
                  />
                </div>
              </div>

              {/* Imagen inferior - similar a la foto de referencia */}
              <div className="absolute bottom-0 left-0 w-[50%] h-[180px] animate-float animation-delay-300" style={{ transform: 'rotate(-2deg)' }}>
                <div className="rounded-3xl overflow-hidden shadow-xl h-full">
                  <img
                    src="/images/desayuno-ip.jpeg"
                    alt="Tradición culinaria"
                    className="w-full h-full object-cover"
                    loading="eager"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6 animate-fade-in animation-delay-200">
            <h3 className="text-3xl text-black font-bold" style={{ fontFamily: "'Work Sans', sans-serif" }}>Pasión por la Gastronomía</h3>
            <p className="text-lg text-gray-800" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>
              En Cien Fuegos, cada platillo cuenta una historia. Nuestros chefs combinan
              técnicas tradicionales con toques modernos para crear una experiencia culinaria
              única que respeta la esencia de la cocina tradicional.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              {[
                {
                  icon: <Utensils className="mx-auto text-cantina-600" size={32} />,
                  title: "Ingredientes Frescos",
                  description: "Seleccionamos los mejores ingredientes de temporada"
                },
                {
                  icon: <Clock className="mx-auto text-cantina-600" size={32} />,
                  title: "Tradición",
                  description: "Recetas transmitidas por generaciones"
                },
                {
                  icon: <Users className="mx-auto text-cantina-600" size={32} />,
                  title: "Ambiente",
                  description: "Espacio acogedor para disfrutar en compañía"
                }
              ].map((item, index) => (
                <div key={index} className="text-center p-4 transform hover:scale-105 transition-all duration-300 animate-float" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className="bg-cantina-50 rounded-full p-4 inline-block mb-3">
                    {item.icon}
                  </div>
                  <h4 className="text-lg mt-3 mb-2 font-bold text-black" style={{ fontFamily: "'Work Sans', sans-serif" }}>{item.title}</h4>
                  <p className="text-gray-800" style={{ fontFamily: "'Work Sans Normal', sans-serif" }}>{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
